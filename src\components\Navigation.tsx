import React from 'react';
import { Ticket, Home, Bus, FileText, DollarSign, Receipt, Users } from 'lucide-react';

interface NavigationProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

export const Navigation: React.FC<NavigationProps> = ({ activeTab, setActiveTab }) => {
  const tabs = [
    { id: 'tickets', label: 'تذاكر العمرة', icon: Ticket },
    { id: 'passengers', label: 'إدارة المسافرين', icon: Users },
    { id: 'accommodation', label: 'إدارة السكن', icon: Home },
    { id: 'bus', label: 'خدمة الباص', icon: Bus },
    { id: 'reports', label: 'كشوفات الرحلات', icon: FileText },
    { id: 'financial', label: 'التقرير المالي', icon: DollarSign },
    { id: 'expenses', label: 'إدارة المصروفات', icon: Receipt },
  ];

  return (
    <nav className="bg-white shadow-md border-b border-gray-200">
      <div className="container mx-auto px-4">
        <div className="flex space-x-2 space-x-reverse overflow-x-auto">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 space-x-reverse px-6 py-4 text-sm font-medium transition-all duration-200 border-b-2 whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-emerald-500 text-emerald-600 bg-emerald-50'
                    : 'border-transparent text-gray-600 hover:text-gray-800 hover:border-gray-300'
                }`}
              >
                <Icon className="h-5 w-5" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>
      </div>
    </nav>
  );
};
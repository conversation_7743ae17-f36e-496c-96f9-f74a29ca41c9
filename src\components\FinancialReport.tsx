import React, { useState } from 'react';
import { DollarSign, TrendingUp, TrendingDown, Calendar, Filter, Printer, Download } from 'lucide-react';
import { getTickets } from '../utils/storage';

interface FinancialData {
  totalRevenue: number;
  totalPaid: number;
  totalRemaining: number;
  totalExpenses: number;
  netProfit: number;
  monthlyData: {
    month: string;
    revenue: number;
    expenses: number;
    profit: number;
  }[];
}

export const FinancialReport: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('current-month');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Calculate financial data from tickets
  const financialData: FinancialData = React.useMemo(() => {
    const tickets = getTickets();
    
    const totalRevenue = tickets.reduce((sum, ticket) => sum + ticket.totalAmount, 0);
    const totalPaid = tickets.reduce((sum, ticket) => sum + ticket.paidAmount, 0);
    const totalRemaining = tickets.reduce((sum, ticket) => sum + ticket.remainingAmount, 0);
    
    // Real expenses data - would come from expenses storage in real app
    const totalExpenses = 0; // No expenses data yet
    const netProfit = totalPaid - totalExpenses;

    return {
      totalRevenue,
      totalPaid,
      totalRemaining,
      totalExpenses,
      netProfit,
      monthlyData: [], // No monthly data yet
    };
  }, []);

  const handlePrint = () => {
    window.print();
  };

  const handleExportPDF = () => {
    console.log('Exporting PDF...');
  };

  const paymentMethodData = [
    { method: 'كاش', amount: 75000, percentage: 62.5 },
    { method: 'فيزا', amount: 35000, percentage: 29.2 },
    { method: 'شبكة', amount: 10000, percentage: 8.3 },
  ];

  return (
    <div className="max-w-7xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl font-bold text-gray-800 flex items-center space-x-2 space-x-reverse">
            <DollarSign className="h-6 w-6" />
            <span>التقرير المالي</span>
          </h2>
          <div className="flex space-x-2 space-x-reverse">
            <button
              onClick={handlePrint}
              className="flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Printer className="h-4 w-4" />
              <span>طباعة</span>
            </button>
            <button
              onClick={handleExportPDF}
              className="flex items-center space-x-2 space-x-reverse bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
            >
              <Download className="h-4 w-4" />
              <span>تصدير PDF</span>
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الفترة الزمنية</label>
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
            >
              <option value="current-month">الشهر الحالي</option>
              <option value="last-month">الشهر الماضي</option>
              <option value="quarter">الربع الحالي</option>
              <option value="year">السنة الحالية</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">فئة التقرير</label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
            >
              <option value="all">جميع الفئات</option>
              <option value="revenue">الإيرادات فقط</option>
              <option value="expenses">المصروفات فقط</option>
              <option value="profit">الأرباح فقط</option>
            </select>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
          <div className="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white p-6 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-emerald-100 text-sm">إجمالي الإيرادات</p>
                <p className="text-2xl font-bold">{financialData.totalRevenue.toLocaleString()}</p>
                <p className="text-emerald-100 text-xs">ريال سعودي</p>
              </div>
              <TrendingUp className="h-8 w-8 text-emerald-200" />
            </div>
          </div>

          <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">المبالغ المدفوعة</p>
                <p className="text-2xl font-bold">{financialData.totalPaid.toLocaleString()}</p>
                <p className="text-blue-100 text-xs">ريال سعودي</p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-200" />
            </div>
          </div>

          <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white p-6 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-yellow-100 text-sm">المبالغ المتبقية</p>
                <p className="text-2xl font-bold">{financialData.totalRemaining.toLocaleString()}</p>
                <p className="text-yellow-100 text-xs">ريال سعودي</p>
              </div>
              <Calendar className="h-8 w-8 text-yellow-200" />
            </div>
          </div>

          <div className="bg-gradient-to-r from-red-500 to-red-600 text-white p-6 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-red-100 text-sm">إجمالي المصروفات</p>
                <p className="text-2xl font-bold">{financialData.totalExpenses.toLocaleString()}</p>
                <p className="text-red-100 text-xs">ريال سعودي</p>
              </div>
              <TrendingDown className="h-8 w-8 text-red-200" />
            </div>
          </div>

          <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-6 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm">صافي الربح</p>
                <p className="text-2xl font-bold">{financialData.netProfit.toLocaleString()}</p>
                <p className="text-purple-100 text-xs">ريال سعودي</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-200" />
            </div>
          </div>
        </div>

        {/* Payment Methods */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">طرق الدفع</h3>
            <div className="space-y-4">
              {paymentMethodData.map((method, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className="w-4 h-4 bg-emerald-500 rounded-full"></div>
                    <span className="font-medium">{method.method}</span>
                  </div>
                  <div className="text-left">
                    <div className="font-bold">{method.amount.toLocaleString()} ريال</div>
                    <div className="text-sm text-gray-600">{method.percentage}%</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">الأداء المالي</h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">معدل التحصيل</span>
                <span className="font-bold text-emerald-600">
                  {((financialData.totalPaid / financialData.totalRevenue) * 100).toFixed(1)}%
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">هامش الربح</span>
                <span className="font-bold text-blue-600">
                  {((financialData.netProfit / financialData.totalRevenue) * 100).toFixed(1)}%
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">نسبة المصروفات</span>
                <span className="font-bold text-red-600">
                  {((financialData.totalExpenses / financialData.totalRevenue) * 100).toFixed(1)}%
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Monthly Performance */}
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">الأداء الشهري</h3>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-right py-2 px-4 font-medium text-gray-700">الشهر</th>
                  <th className="text-right py-2 px-4 font-medium text-gray-700">الإيرادات</th>
                  <th className="text-right py-2 px-4 font-medium text-gray-700">المصروفات</th>
                  <th className="text-right py-2 px-4 font-medium text-gray-700">الربح</th>
                  <th className="text-right py-2 px-4 font-medium text-gray-700">هامش الربح</th>
                </tr>
              </thead>
              <tbody>
                {financialData.monthlyData.map((month, index) => (
                  <tr key={index} className="border-b border-gray-100 hover:bg-white">
                    <td className="py-3 px-4 font-medium">{month.month}</td>
                    <td className="py-3 px-4 text-emerald-600 font-medium">
                      {month.revenue.toLocaleString()} ريال
                    </td>
                    <td className="py-3 px-4 text-red-600 font-medium">
                      {month.expenses.toLocaleString()} ريال
                    </td>
                    <td className="py-3 px-4 text-blue-600 font-medium">
                      {month.profit.toLocaleString()} ريال
                    </td>
                    <td className="py-3 px-4 text-purple-600 font-medium">
                      {((month.profit / month.revenue) * 100).toFixed(1)}%
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Summary */}
        <div className="mt-8 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">ملخص الأداء المالي</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-700 mb-2">نقاط القوة:</h4>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• معدل تحصيل جيد ({((financialData.totalPaid / financialData.totalRevenue) * 100).toFixed(1)}%)</li>
                <li>• هامش ربح مناسب ({((financialData.netProfit / financialData.totalRevenue) * 100).toFixed(1)}%)</li>
                <li>• تنوع في طرق الدفع</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-700 mb-2">نقاط التحسين:</h4>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• متابعة المبالغ المتبقية ({financialData.totalRemaining.toLocaleString()} ريال)</li>
                <li>• تحسين كفاءة المصروفات</li>
                <li>• زيادة الإيرادات الشهرية</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
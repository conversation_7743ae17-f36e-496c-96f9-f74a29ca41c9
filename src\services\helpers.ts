import { supabase } from '../lib/supabase'
import { tripService, ticketService, companionService, roomService, roomAssignmentService } from './database'

// Helper functions for complex operations

// Generate unique trip number
export const generateTripNumber = async (): Promise<string> => {
  const year = new Date().getFullYear()
  const trips = await tripService.getAll()
  
  const existingNumbers = trips
    .map(trip => parseInt(trip.trip_number.split('-')[2]))
    .filter(num => !isNaN(num))
  
  const nextNumber = Math.max(...existingNumbers, 0) + 1
  return `UM-${year}-${nextNumber.toString().padStart(3, '0')}`
}

// Get occupied seats for a trip
export const getOccupiedSeats = async (tripId: string): Promise<number[]> => {
  const tickets = await ticketService.getByTrip(tripId)
  const occupiedSeats: number[] = []
  
  for (const ticket of tickets) {
    // Add main passenger seat
    if (ticket.seat_number) {
      occupiedSeats.push(parseInt(ticket.seat_number))
    }
    
    // Add companions seats
    const companions = await companionService.getByTicket(ticket.id)
    companions.forEach(companion => {
      if (companion.seat_number) {
        occupiedSeats.push(parseInt(companion.seat_number))
      }
    })
  }
  
  return [...new Set(occupiedSeats)].sort((a, b) => a - b)
}

// Get available seats for a trip
export const getAvailableSeats = async (tripId: string, maxCapacity: number = 49): Promise<number[]> => {
  const occupiedSeats = await getOccupiedSeats(tripId)
  const availableSeats: number[] = []
  
  for (let i = 1; i <= maxCapacity; i++) {
    if (!occupiedSeats.includes(i)) {
      availableSeats.push(i)
    }
  }
  
  return availableSeats
}

// Auto-assign seats for passenger and companions
export const autoAssignSeats = async (
  tripId: string, 
  companionsCount: number
): Promise<{ mainSeat: number; companionSeats: number[] } | null> => {
  const availableSeats = await getAvailableSeats(tripId)
  const totalSeatsNeeded = 1 + companionsCount
  
  if (availableSeats.length < totalSeatsNeeded) {
    return null // Not enough seats available
  }
  
  // Try to assign consecutive seats when possible
  const assignedSeats = availableSeats.slice(0, totalSeatsNeeded)
  
  return {
    mainSeat: assignedSeats[0],
    companionSeats: assignedSeats.slice(1)
  }
}

// Create complete ticket with companions
export const createTicketWithCompanions = async (
  ticketData: any,
  companions: Array<{ name: string; id_number: string; nationality: string; seat_number?: string }>
): Promise<{ ticket: any; companions: any[] }> => {
  try {
    // Create the main ticket
    const ticket = await ticketService.create(ticketData)
    
    // Create companions
    const createdCompanions = []
    for (const companionData of companions) {
      const companion = await companionService.create({
        ticket_id: ticket.id,
        name: companionData.name,
        id_number: companionData.id_number,
        nationality: companionData.nationality,
        seat_number: companionData.seat_number
      })
      createdCompanions.push(companion)
    }
    
    // Update trip passenger count
    const trip = await tripService.getByNumber(ticket.trip_number)
    if (trip) {
      await tripService.update(trip.id, {
        passenger_count: trip.passenger_count + 1 + companions.length
      })
    }
    
    return { ticket, companions: createdCompanions }
  } catch (error) {
    console.error('Error creating ticket with companions:', error)
    throw error
  }
}

// Get passengers needing accommodation
export const getPassengersNeedingAccommodation = async (): Promise<any[]> => {
  const tickets = await ticketService.getAll()
  const result = []
  
  for (const ticket of tickets) {
    if (ticket.accommodation_type !== 'none') {
      // Check if already assigned to a room
      const assignment = await roomAssignmentService.getByTicket(ticket.id)
      if (!assignment) {
        const companions = await companionService.getByTicket(ticket.id)
        result.push({
          ...ticket,
          companions
        })
      }
    }
  }
  
  return result
}

// Get room with guests details
export const getRoomWithGuests = async (roomId: string): Promise<any> => {
  const room = await roomService.getAll()
  const targetRoom = room.find(r => r.id === roomId)
  if (!targetRoom) return null
  
  const assignments = await roomAssignmentService.getByRoom(roomId)
  const guests = []
  
  for (const assignment of assignments) {
    const tickets = await ticketService.getAll()
    const ticket = tickets.find(t => t.id === assignment.ticket_id)
    if (ticket) {
      const companions = await companionService.getByTicket(ticket.id)
      guests.push({
        ...ticket,
        companions
      })
    }
  }
  
  return {
    ...targetRoom,
    guests
  }
}

// Get trip with full details (passengers, companions, drivers)
export const getTripWithDetails = async (tripId: string): Promise<any> => {
  const trip = await tripService.getAll()
  const targetTrip = trip.find(t => t.id === tripId)
  if (!targetTrip) return null
  
  const tickets = await ticketService.getByTrip(tripId)
  const passengers = []
  
  for (const ticket of tickets) {
    const companions = await companionService.getByTicket(ticket.id)
    passengers.push({
      ...ticket,
      companions
    })
  }
  
  return {
    ...targetTrip,
    passengers
  }
}

// Update room occupancy after assignment changes
export const updateRoomOccupancy = async (roomId: string): Promise<void> => {
  const assignments = await roomAssignmentService.getByRoom(roomId)
  const rooms = await roomService.getAll()
  const room = rooms.find(r => r.id === roomId)
  
  if (room) {
    await roomService.update(roomId, {
      occupied: assignments.length
    })
  }
}

// Assign passenger to room with validation
export const assignPassengerToRoom = async (
  roomId: string, 
  ticketId: string
): Promise<boolean> => {
  try {
    const rooms = await roomService.getAll()
    const room = rooms.find(r => r.id === roomId)
    
    if (!room) {
      throw new Error('Room not found')
    }
    
    const assignments = await roomAssignmentService.getByRoom(roomId)
    
    if (assignments.length >= room.capacity) {
      throw new Error('Room is full')
    }
    
    // Check if passenger is already assigned to another room
    const existingAssignment = await roomAssignmentService.getByTicket(ticketId)
    if (existingAssignment) {
      // Remove from previous room
      await roomAssignmentService.remove(ticketId)
      await updateRoomOccupancy(existingAssignment.room_id)
    }
    
    // Assign to new room
    await roomAssignmentService.assign(roomId, ticketId)
    await updateRoomOccupancy(roomId)
    
    return true
  } catch (error) {
    console.error('Error assigning passenger to room:', error)
    return false
  }
}

// Remove passenger from room
export const removePassengerFromRoom = async (ticketId: string): Promise<boolean> => {
  try {
    const assignment = await roomAssignmentService.getByTicket(ticketId)
    if (assignment) {
      await roomAssignmentService.remove(ticketId)
      await updateRoomOccupancy(assignment.room_id)
    }
    return true
  } catch (error) {
    console.error('Error removing passenger from room:', error)
    return false
  }
}

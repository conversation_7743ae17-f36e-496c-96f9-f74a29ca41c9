// Storage utilities - now using Supabase instead of localStorage
import {
  tripService,
  ticketService,
  companionService,
  roomService,
  roomAssignmentService,
  driverService,
  expenseService
} from '../services/database'
import {
  generateTripNumber as generateTripNumberFromDB,
  createTicketWithCompanions,
  getPassengersNeedingAccommodation,
  assignPassengerToRoom as assignToRoom,
  removePassengerFromRoom as removeFromRoom,
  getOccupiedSeats as getOccupiedSeatsFromDB,
  getAvailableSeats as getAvailableSeatsFromDB,
  autoAssignSeats as autoAssignSeatsFromDB
} from '../services/helpers'

// Legacy interfaces for backward compatibility
export interface StoredTicket {
  id: string;
  tripNumber: string;
  passengerName: string;
  passengerNameEn: string;
  nationality: string;
  phoneNumber: string;
  idNumber: string;
  departureDate: string;
  tripType: 'one-way' | 'return' | 'round-trip';
  returnDate: string;
  departureTime: string;
  accommodationType: 'individual' | 'shared' | 'family' | 'none';
  seatNumber: string;
  paymentMethod: 'cash' | 'visa' | 'network';
  paidAmount: number;
  remainingAmount: number;
  totalAmount: number;
  companions: Array<{
    name: string;
    idNumber: string;
    nationality: string;
    seatNumber?: string;
  }>;
  createdAt: string;
}

export interface StoredTrip {
  tripNumber: string;
  passengerCount: number;
  maxCapacity: number;
  passengers: string[]; // ticket IDs
  occupiedSeats: number[];
  createdAt: string;
}

export interface StoredRoom {
  id: string;
  type: 'individual' | 'shared' | 'family';
  capacity: number;
  occupied: number;
  guests: string[]; // ticket IDs
}

// Tickets management
// Updated functions to use Supabase
export const saveTicket = async (ticket: StoredTicket): Promise<void> => {
  try {
    const tripData = await tripService.getByNumber(ticket.tripNumber);
    if (!tripData) {
      throw new Error('Trip not found');
    }

    const ticketData = {
      trip_id: tripData.id,
      trip_number: ticket.tripNumber,
      passenger_name: ticket.passengerName,
      passenger_name_en: ticket.passengerNameEn,
      nationality: ticket.nationality,
      phone_number: ticket.phoneNumber,
      id_number: ticket.idNumber,
      departure_date: ticket.departureDate,
      trip_type: ticket.tripType,
      return_date: ticket.returnDate,
      departure_time: ticket.departureTime,
      accommodation_type: ticket.accommodationType,
      seat_number: ticket.seatNumber,
      payment_method: ticket.paymentMethod,
      paid_amount: ticket.paidAmount,
      remaining_amount: ticket.remainingAmount,
      total_amount: ticket.totalAmount,
      status: 'active'
    };

    await createTicketWithCompanions(ticketData, ticket.companions);
  } catch (error) {
    console.error('Error saving ticket:', error);
    throw error;
  }
};

export const getTickets = async (): Promise<StoredTicket[]> => {
  try {
    const tickets = await ticketService.getAll();
    const result: StoredTicket[] = [];

    for (const ticket of tickets) {
      const companions = await companionService.getByTicket(ticket.id);

      result.push({
        id: ticket.id,
        tripNumber: ticket.trip_number,
        passengerName: ticket.passenger_name,
        passengerNameEn: ticket.passenger_name_en || '',
        nationality: ticket.nationality,
        phoneNumber: ticket.phone_number,
        idNumber: ticket.id_number,
        departureDate: ticket.departure_date,
        tripType: ticket.trip_type,
        returnDate: ticket.return_date || '',
        departureTime: ticket.departure_time || '',
        accommodationType: ticket.accommodation_type,
        seatNumber: ticket.seat_number || '',
        paymentMethod: ticket.payment_method,
        paidAmount: ticket.paid_amount,
        remainingAmount: ticket.remaining_amount,
        totalAmount: ticket.total_amount,
        companions: companions.map(c => ({
          name: c.name,
          idNumber: c.id_number,
          nationality: c.nationality,
          seatNumber: c.seat_number
        })),
        createdAt: ticket.created_at
      });
    }

    return result;
  } catch (error) {
    console.error('Error getting tickets:', error);
    return [];
  }
};

export const getTicketById = (id: string): StoredTicket | null => {
  const tickets = getTickets();
  return tickets.find(t => t.id === id) || null;
};

export const deleteTicket = (id: string): void => {
  const tickets = getTickets().filter(t => t.id !== id);
  localStorage.setItem('umrah_tickets', JSON.stringify(tickets));
};

// Trips management
export const saveTrip = async (trip: StoredTrip): Promise<void> => {
  try {
    const existingTrip = await tripService.getByNumber(trip.tripNumber);

    const tripData = {
      trip_number: trip.tripNumber,
      passenger_count: trip.passengerCount,
      max_capacity: trip.maxCapacity,
      occupied_seats: trip.occupiedSeats,
      status: 'active'
    };

    if (existingTrip) {
      await tripService.update(existingTrip.id, tripData);
    } else {
      await tripService.create(tripData);
    }
  } catch (error) {
    console.error('Error saving trip:', error);
    throw error;
  }
};

export const getTrips = async (): Promise<StoredTrip[]> => {
  try {
    const trips = await tripService.getAll();
    return trips.map(trip => ({
      tripNumber: trip.trip_number,
      passengerCount: trip.passenger_count,
      maxCapacity: trip.max_capacity,
      passengers: [], // Will be populated separately if needed
      occupiedSeats: trip.occupied_seats || [],
      createdAt: trip.created_at
    }));
  } catch (error) {
    console.error('Error getting trips:', error);
    return [];
  }
};

export const getTripByNumber = async (tripNumber: string): Promise<StoredTrip | null> => {
  try {
    const trip = await tripService.getByNumber(tripNumber);
    if (!trip) return null;

    return {
      tripNumber: trip.trip_number,
      passengerCount: trip.passenger_count,
      maxCapacity: trip.max_capacity,
      passengers: [], // Will be populated separately if needed
      occupiedSeats: trip.occupied_seats || [],
      createdAt: trip.created_at
    };
  } catch (error) {
    console.error('Error getting trip by number:', error);
    return null;
  }
};

// Rooms management
export const saveRoom = async (room: StoredRoom): Promise<void> => {
  try {
    const rooms = await roomService.getAll();
    const existingRoom = rooms.find(r => r.room_number === room.id);

    const roomData = {
      room_number: room.id,
      room_type: room.type,
      capacity: room.capacity,
      occupied: room.occupied,
      status: 'available'
    };

    if (existingRoom) {
      await roomService.update(existingRoom.id, roomData);
    } else {
      await roomService.create(roomData);
    }
  } catch (error) {
    console.error('Error saving room:', error);
    throw error;
  }
};

export const getRooms = async (): Promise<StoredRoom[]> => {
  try {
    const rooms = await roomService.getAll();
    return rooms.map(room => ({
      id: room.room_number,
      type: room.room_type,
      capacity: room.capacity,
      occupied: room.occupied,
      guests: [] // Will be populated separately if needed
    }));
  } catch (error) {
    console.error('Error getting rooms:', error);
    return [];
  }
};

// Auto-assign accommodation
export const assignAccommodation = (ticketId: string, accommodationType: 'individual' | 'shared' | 'family'): string | null => {
  if (accommodationType === 'none') return null;
  
  const rooms = getRooms();
  const availableRoom = rooms.find(room => 
    room.type === accommodationType && room.occupied < room.capacity
  );
  
  if (availableRoom) {
    availableRoom.guests.push(ticketId);
    availableRoom.occupied += 1;
    saveRoom(availableRoom);
    return availableRoom.id;
  }
  
  return null;
};

// Generate unique ID
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Generate trip number
export const generateTripNumber = async (): Promise<string> => {
  return await generateTripNumberFromDB();
};

// Get occupied seats for a trip
export const getOccupiedSeats = async (tripNumber: string): Promise<number[]> => {
  try {
    const trip = await tripService.getByNumber(tripNumber);
    if (!trip) return [];

    return await getOccupiedSeatsFromDB(trip.id);
  } catch (error) {
    console.error('Error getting occupied seats:', error);
    return [];
  }
};

// Get available seats for a trip
export const getAvailableSeats = async (tripNumber: string, maxCapacity: number = 49): Promise<number[]> => {
  try {
    const trip = await tripService.getByNumber(tripNumber);
    if (!trip) return [];

    return await getAvailableSeatsFromDB(trip.id, maxCapacity);
  } catch (error) {
    console.error('Error getting available seats:', error);
    return [];
  }
};

// Reserve seats for passenger and companions
export const reserveSeats = async (tripNumber: string, mainSeat: number, companionSeats: number[]): Promise<boolean> => {
  try {
    const occupiedSeats = await getOccupiedSeats(tripNumber);
    const allRequestedSeats = [mainSeat, ...companionSeats];

    // Check if any requested seat is already occupied
    const hasConflict = allRequestedSeats.some(seat => occupiedSeats.includes(seat));

    return !hasConflict; // Return true if no conflict
  } catch (error) {
    console.error('Error reserving seats:', error);
    return false;
  }
};

// Auto-assign seats for passenger and companions
export const autoAssignSeats = async (tripNumber: string, companionsCount: number): Promise<{ mainSeat: number; companionSeats: number[] } | null> => {
  try {
    const trip = await tripService.getByNumber(tripNumber);
    if (!trip) return null;

    return await autoAssignSeatsFromDB(trip.id, companionsCount);
  } catch (error) {
    console.error('Error auto-assigning seats:', error);
    return null;
  }
};

// Clear all data functions - now using Supabase
export const clearAllTickets = async (): Promise<void> => {
  try {
    const tickets = await ticketService.getAll();
    for (const ticket of tickets) {
      await ticketService.delete(ticket.id);
    }
  } catch (error) {
    console.error('Error clearing tickets:', error);
  }
};

export const clearAllTrips = async (): Promise<void> => {
  try {
    const trips = await tripService.getAll();
    for (const trip of trips) {
      await tripService.delete(trip.id);
    }
  } catch (error) {
    console.error('Error clearing trips:', error);
  }
};

export const clearAllRooms = async (): Promise<void> => {
  try {
    const rooms = await roomService.getAll();
    for (const room of rooms) {
      await roomService.delete(room.id);
    }
  } catch (error) {
    console.error('Error clearing rooms:', error);
  }
};

export const clearAllData = async (): Promise<void> => {
  await clearAllTickets();
  await clearAllTrips();
  await clearAllRooms();
};

// Initialize with empty data - no mock data
export const initializeEmptyData = async (): Promise<void> => {
  console.log('Database is now connected to Supabase - no local data to clear');
};

// Legacy functions for backward compatibility
export const generateId = (): string => {
  return crypto.randomUUID();
};

export const updateTripPassengerCount = async (tripNumber: string, increment: boolean): Promise<void> => {
  try {
    const trip = await tripService.getByNumber(tripNumber);
    if (trip) {
      const newCount = increment ? trip.passenger_count + 1 : Math.max(0, trip.passenger_count - 1);
      await tripService.update(trip.id, { passenger_count: newCount });
    }
  } catch (error) {
    console.error('Error updating trip passenger count:', error);
  }
};

export const assignAccommodation = async (ticketId: string, roomId: string): Promise<void> => {
  try {
    await assignToRoom(roomId, ticketId);
  } catch (error) {
    console.error('Error assigning accommodation:', error);
  }
};
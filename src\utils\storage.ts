// Local storage utilities for managing application data
export interface StoredTicket {
  id: string;
  tripNumber: string;
  passengerName: string;
  passengerNameEn: string;
  nationality: string;
  phoneNumber: string;
  idNumber: string;
  departureDate: string;
  tripType: 'one-way' | 'return' | 'round-trip';
  returnDate: string;
  departureTime: string;
  accommodationType: 'individual' | 'shared' | 'family' | 'none';
  seatNumber: string;
  paymentMethod: 'cash' | 'visa' | 'network';
  paidAmount: number;
  remainingAmount: number;
  totalAmount: number;
  companions: Array<{
    name: string;
    idNumber: string;
    nationality: string;
    seatNumber?: string;
  }>;
  createdAt: string;
}

export interface StoredTrip {
  tripNumber: string;
  passengerCount: number;
  maxCapacity: number;
  passengers: string[]; // ticket IDs
  occupiedSeats: number[];
  createdAt: string;
}

export interface StoredRoom {
  id: string;
  type: 'individual' | 'shared' | 'family';
  capacity: number;
  occupied: number;
  guests: string[]; // ticket IDs
}

// Tickets management
export const saveTicket = (ticket: StoredTicket): void => {
  const tickets = getTickets();
  const existingIndex = tickets.findIndex(t => t.id === ticket.id);
  
  if (existingIndex >= 0) {
    tickets[existingIndex] = ticket;
  } else {
    tickets.push(ticket);
  }
  
  localStorage.setItem('umrah_tickets', JSON.stringify(tickets));
};

export const getTickets = (): StoredTicket[] => {
  const stored = localStorage.getItem('umrah_tickets');
  return stored ? JSON.parse(stored) : [];
};

export const getTicketById = (id: string): StoredTicket | null => {
  const tickets = getTickets();
  return tickets.find(t => t.id === id) || null;
};

export const deleteTicket = (id: string): void => {
  const tickets = getTickets().filter(t => t.id !== id);
  localStorage.setItem('umrah_tickets', JSON.stringify(tickets));
};

// Trips management
export const saveTrip = (trip: StoredTrip): void => {
  const trips = getTrips();
  const existingIndex = trips.findIndex(t => t.tripNumber === trip.tripNumber);
  
  if (existingIndex >= 0) {
    trips[existingIndex] = trip;
  } else {
    trips.push(trip);
  }
  
  localStorage.setItem('umrah_trips', JSON.stringify(trips));
};

export const getTrips = (): StoredTrip[] => {
  const stored = localStorage.getItem('umrah_trips');
  return stored ? JSON.parse(stored) : [
    { tripNumber: 'UM-2024-001', passengerCount: 45, maxCapacity: 49, passengers: [], occupiedSeats: [5, 12, 23, 31, 45], createdAt: new Date().toISOString() },
    { tripNumber: 'UM-2024-002', passengerCount: 49, maxCapacity: 49, passengers: [], occupiedSeats: [], createdAt: new Date().toISOString() },
    { tripNumber: 'UM-2024-003', passengerCount: 32, maxCapacity: 49, passengers: [], occupiedSeats: [], createdAt: new Date().toISOString() },
  ];
};

export const getTripByNumber = (tripNumber: string): StoredTrip | null => {
  const trips = getTrips();
  return trips.find(t => t.tripNumber === tripNumber) || null;
};

// Rooms management
export const saveRoom = (room: StoredRoom): void => {
  const rooms = getRooms();
  const existingIndex = rooms.findIndex(r => r.id === room.id);
  
  if (existingIndex >= 0) {
    rooms[existingIndex] = room;
  } else {
    rooms.push(room);
  }
  
  localStorage.setItem('umrah_rooms', JSON.stringify(rooms));
};

export const getRooms = (): StoredRoom[] => {
  const stored = localStorage.getItem('umrah_rooms');
  return stored ? JSON.parse(stored) : [
    { id: '101', type: 'individual', capacity: 1, occupied: 1, guests: [] },
    { id: '102', type: 'individual', capacity: 1, occupied: 0, guests: [] },
    { id: '201', type: 'shared', capacity: 4, occupied: 2, guests: [] },
    { id: '202', type: 'shared', capacity: 4, occupied: 3, guests: [] },
    { id: '301', type: 'family', capacity: 6, occupied: 4, guests: [] },
    { id: '302', type: 'family', capacity: 6, occupied: 0, guests: [] },
  ];
};

// Auto-assign accommodation
export const assignAccommodation = (ticketId: string, accommodationType: 'individual' | 'shared' | 'family'): string | null => {
  if (accommodationType === 'none') return null;
  
  const rooms = getRooms();
  const availableRoom = rooms.find(room => 
    room.type === accommodationType && room.occupied < room.capacity
  );
  
  if (availableRoom) {
    availableRoom.guests.push(ticketId);
    availableRoom.occupied += 1;
    saveRoom(availableRoom);
    return availableRoom.id;
  }
  
  return null;
};

// Generate unique ID
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Generate trip number
export const generateTripNumber = (): string => {
  const year = new Date().getFullYear();
  const trips = getTrips();
  const existingNumbers = trips.map(trip =>
    parseInt(trip.tripNumber.split('-')[2])
  );
  const nextNumber = Math.max(...existingNumbers, 0) + 1;
  return `UM-${year}-${nextNumber.toString().padStart(3, '0')}`;
};

// Get occupied seats for a trip
export const getOccupiedSeats = (tripNumber: string): number[] => {
  const trip = getTripByNumber(tripNumber);
  if (!trip) return [];

  const tickets = getTickets().filter(ticket => ticket.tripNumber === tripNumber);
  const occupiedSeats: number[] = [];

  tickets.forEach(ticket => {
    // Add main passenger seat
    if (ticket.seatNumber) {
      occupiedSeats.push(parseInt(ticket.seatNumber));
    }

    // Add companions seats
    ticket.companions.forEach(companion => {
      if (companion.seatNumber) {
        occupiedSeats.push(parseInt(companion.seatNumber));
      }
    });
  });

  return [...new Set(occupiedSeats)].sort((a, b) => a - b);
};

// Get available seats for a trip
export const getAvailableSeats = (tripNumber: string, maxCapacity: number = 49): number[] => {
  const occupiedSeats = getOccupiedSeats(tripNumber);
  const availableSeats: number[] = [];

  for (let i = 1; i <= maxCapacity; i++) {
    if (!occupiedSeats.includes(i)) {
      availableSeats.push(i);
    }
  }

  return availableSeats;
};

// Reserve seats for passenger and companions
export const reserveSeats = (tripNumber: string, mainSeat: number, companionSeats: number[]): boolean => {
  const occupiedSeats = getOccupiedSeats(tripNumber);
  const allRequestedSeats = [mainSeat, ...companionSeats];

  // Check if any requested seat is already occupied
  const hasConflict = allRequestedSeats.some(seat => occupiedSeats.includes(seat));

  if (hasConflict) {
    return false; // Cannot reserve seats
  }

  return true; // Seats can be reserved
};

// Auto-assign seats for passenger and companions
export const autoAssignSeats = (tripNumber: string, companionsCount: number): { mainSeat: number; companionSeats: number[] } | null => {
  const availableSeats = getAvailableSeats(tripNumber);
  const totalSeatsNeeded = 1 + companionsCount;

  if (availableSeats.length < totalSeatsNeeded) {
    return null; // Not enough seats available
  }

  // Try to assign consecutive seats when possible
  const assignedSeats = availableSeats.slice(0, totalSeatsNeeded);

  return {
    mainSeat: assignedSeats[0],
    companionSeats: assignedSeats.slice(1)
  };
};
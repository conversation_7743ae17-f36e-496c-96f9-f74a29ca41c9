import React, { useState } from 'react';
import { Navigation } from './components/Navigation';
import { TicketForm } from './components/TicketForm';
import { PassengerManagement } from './components/PassengerManagement';
import { AccommodationManagement } from './components/AccommodationManagement';
import { BusService } from './components/BusService';
import { TripReports } from './components/TripReports';
import { FinancialReport } from './components/FinancialReport';
import { ExpenseManagement } from './components/ExpenseManagement';
import { Header } from './components/Header';

function App() {
  const [activeTab, setActiveTab] = useState('tickets');

  const renderContent = () => {
    switch (activeTab) {
      case 'tickets':
        return <TicketForm onTicketSaved={(accommodationType) => {
          if (accommodationType !== 'none') {
            setActiveTab('accommodation');
          }
        }} />;
      case 'passengers':
        return <PassengerManagement />;
      case 'accommodation':
        return <AccommodationManagement />;
      case 'bus':
        return <BusService />;
      case 'reports':
        return <TripReports />;
      case 'financial':
        return <FinancialReport />;
      case 'expenses':
        return <ExpenseManagement />;
      default:
        return <TicketForm onTicketSaved={(accommodationType) => {
          if (accommodationType !== 'none') {
            setActiveTab('accommodation');
          }
        }} />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 to-blue-50" dir="rtl">
      <Header />
      <Navigation activeTab={activeTab} setActiveTab={setActiveTab} />
      <main className="container mx-auto px-4 py-8">
        {renderContent()}
      </main>
    </div>
  );
}

export default App;
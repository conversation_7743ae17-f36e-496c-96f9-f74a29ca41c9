import React, { useState, useEffect } from 'react';
import { Users, Search, Edit, Trash2, Printer, Download, Eye, Plus } from 'lucide-react';
import { getTickets, deleteTicket, saveTicket, StoredTicket } from '../utils/storage';
import { printTicket, exportToPDF } from '../utils/printUtils';

export const PassengerManagement: React.FC = () => {
  const [passengers, setPassengers] = useState<StoredTicket[]>([]);
  const [filteredPassengers, setFilteredPassengers] = useState<StoredTicket[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterTrip, setFilterTrip] = useState('all');
  const [selectedPassenger, setSelectedPassenger] = useState<StoredTicket | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);

  // Load passengers on component mount
  useEffect(() => {
    loadPassengers();
  }, []);

  // Filter passengers when search term or filter changes
  useEffect(() => {
    filterPassengers();
  }, [passengers, searchTerm, filterTrip]);

  const loadPassengers = () => {
    const tickets = getTickets();
    setPassengers(tickets);
  };

  const filterPassengers = () => {
    let filtered = passengers;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(passenger =>
        passenger.passengerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        passenger.tripNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        passenger.phoneNumber.includes(searchTerm) ||
        passenger.idNumber.includes(searchTerm)
      );
    }

    // Filter by trip
    if (filterTrip !== 'all') {
      filtered = filtered.filter(passenger => passenger.tripNumber === filterTrip);
    }

    setFilteredPassengers(filtered);
  };

  const handleEdit = (passenger: StoredTicket) => {
    setSelectedPassenger(passenger);
    setShowEditModal(true);
  };

  const handleView = (passenger: StoredTicket) => {
    setSelectedPassenger(passenger);
    setShowViewModal(true);
  };

  const handleDelete = (passenger: StoredTicket) => {
    if (window.confirm(`هل أنت متأكد من حذف تذكرة ${passenger.passengerName}؟`)) {
      deleteTicket(passenger.id);
      loadPassengers();
    }
  };

  const handleSaveEdit = (updatedPassenger: StoredTicket) => {
    saveTicket(updatedPassenger);
    loadPassengers();
    setShowEditModal(false);
    setSelectedPassenger(null);
  };

  const handlePrint = (passenger: StoredTicket) => {
    printTicket(passenger);
  };

  const handleExportPDF = (passenger: StoredTicket) => {
    exportToPDF(passenger);
  };

  const handlePrintAll = () => {
    if (filteredPassengers.length === 0) {
      alert('لا توجد تذاكر للطباعة');
      return;
    }
    
    // Print all filtered passengers
    filteredPassengers.forEach((passenger, index) => {
      setTimeout(() => {
        printTicket(passenger);
      }, index * 1000); // Delay between prints
    });
  };

  // Get unique trip numbers for filter
  const uniqueTrips = [...new Set(passengers.map(p => p.tripNumber))];

  const getAccommodationTypeLabel = (type: string) => {
    switch (type) {
      case 'individual': return 'فردي';
      case 'shared': return 'مشترك';
      case 'family': return 'عوائل';
      case 'none': return 'بدون سكن';
      default: return 'غير محدد';
    }
  };

  const getPaymentMethodLabel = (method: string) => {
    switch (method) {
      case 'cash': return 'كاش';
      case 'visa': return 'فيزا';
      case 'network': return 'شبكة';
      default: return 'غير محدد';
    }
  };

  return (
    <div className="max-w-7xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl font-bold text-gray-800 flex items-center space-x-2 space-x-reverse">
            <Users className="h-6 w-6" />
            <span>إدارة المسافرين</span>
          </h2>
          <div className="flex space-x-2 space-x-reverse">
            <button
              onClick={handlePrintAll}
              className="flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Printer className="h-4 w-4" />
              <span>طباعة الكل</span>
            </button>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-lg">
            <div className="text-2xl font-bold">{passengers.length}</div>
            <div className="text-blue-100">إجمالي المسافرين</div>
          </div>
          <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-lg">
            <div className="text-2xl font-bold">{uniqueTrips.length}</div>
            <div className="text-green-100">عدد الرحلات</div>
          </div>
          <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-lg">
            <div className="text-2xl font-bold">
              {passengers.reduce((sum, p) => sum + p.totalAmount, 0).toLocaleString()}
            </div>
            <div className="text-purple-100">إجمالي الإيرادات</div>
          </div>
          <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white p-4 rounded-lg">
            <div className="text-2xl font-bold">
              {passengers.reduce((sum, p) => sum + p.remainingAmount, 0).toLocaleString()}
            </div>
            <div className="text-yellow-100">المبالغ المتبقية</div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-gray-50 rounded-lg p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">البحث</label>
              <div className="relative">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                  placeholder="البحث بالاسم، رقم الرحلة، الجوال، أو الهوية..."
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">تصفية حسب الرحلة</label>
              <select
                value={filterTrip}
                onChange={(e) => setFilterTrip(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
              >
                <option value="all">جميع الرحلات</option>
                {uniqueTrips.map(trip => (
                  <option key={trip} value={trip}>{trip}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Passengers Table */}
        <div className="overflow-x-auto">
          <table className="w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-gray-50">
                <th className="border border-gray-300 px-4 py-3 text-right">رقم الرحلة</th>
                <th className="border border-gray-300 px-4 py-3 text-right">اسم المسافر</th>
                <th className="border border-gray-300 px-4 py-3 text-right">رقم الجوال</th>
                <th className="border border-gray-300 px-4 py-3 text-right">المقعد</th>
                <th className="border border-gray-300 px-4 py-3 text-right">نوع السكن</th>
                <th className="border border-gray-300 px-4 py-3 text-right">المبلغ الإجمالي</th>
                <th className="border border-gray-300 px-4 py-3 text-right">المتبقي</th>
                <th className="border border-gray-300 px-4 py-3 text-center">الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {filteredPassengers.length === 0 ? (
                <tr>
                  <td colSpan={8} className="border border-gray-300 px-4 py-8 text-center text-gray-500">
                    لا توجد تذاكر لعرضها
                  </td>
                </tr>
              ) : (
                filteredPassengers.map((passenger) => (
                  <tr key={passenger.id} className="hover:bg-gray-50">
                    <td className="border border-gray-300 px-4 py-3 font-medium text-emerald-600">
                      {passenger.tripNumber}
                    </td>
                    <td className="border border-gray-300 px-4 py-3 font-medium">
                      {passenger.passengerName}
                    </td>
                    <td className="border border-gray-300 px-4 py-3">
                      {passenger.phoneNumber}
                    </td>
                    <td className="border border-gray-300 px-4 py-3 text-center font-medium">
                      {passenger.seatNumber}
                    </td>
                    <td className="border border-gray-300 px-4 py-3">
                      {getAccommodationTypeLabel(passenger.accommodationType)}
                    </td>
                    <td className="border border-gray-300 px-4 py-3 font-medium text-green-600">
                      {passenger.totalAmount.toLocaleString()} ريال
                    </td>
                    <td className="border border-gray-300 px-4 py-3 font-medium text-red-600">
                      {passenger.remainingAmount.toLocaleString()} ريال
                    </td>
                    <td className="border border-gray-300 px-4 py-3">
                      <div className="flex items-center justify-center space-x-1 space-x-reverse">
                        <button
                          onClick={() => handleView(passenger)}
                          className="text-blue-600 hover:text-blue-800 transition-colors p-1"
                          title="عرض"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleEdit(passenger)}
                          className="text-green-600 hover:text-green-800 transition-colors p-1"
                          title="تعديل"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handlePrint(passenger)}
                          className="text-purple-600 hover:text-purple-800 transition-colors p-1"
                          title="طباعة"
                        >
                          <Printer className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleExportPDF(passenger)}
                          className="text-indigo-600 hover:text-indigo-800 transition-colors p-1"
                          title="تصدير PDF"
                        >
                          <Download className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(passenger)}
                          className="text-red-600 hover:text-red-800 transition-colors p-1"
                          title="حذف"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* View Modal */}
        {showViewModal && selectedPassenger && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl p-8 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-800">تفاصيل المسافر</h3>
                <button
                  onClick={() => setShowViewModal(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">رقم الرحلة</label>
                    <div className="mt-1 text-emerald-600 font-medium">{selectedPassenger.tripNumber}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">اسم المسافر</label>
                    <div className="mt-1">{selectedPassenger.passengerName}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">الاسم بالإنجليزية</label>
                    <div className="mt-1">{selectedPassenger.passengerNameEn}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">الجنسية</label>
                    <div className="mt-1">{selectedPassenger.nationality}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">رقم الجوال</label>
                    <div className="mt-1">{selectedPassenger.phoneNumber}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">رقم الهوية</label>
                    <div className="mt-1">{selectedPassenger.idNumber}</div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">تاريخ الذهاب</label>
                    <div className="mt-1">{new Date(selectedPassenger.departureDate).toLocaleDateString('ar-SA')}</div>
                  </div>
                  {selectedPassenger.returnDate && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">تاريخ العودة</label>
                      <div className="mt-1">{new Date(selectedPassenger.returnDate).toLocaleDateString('ar-SA')}</div>
                    </div>
                  )}
                  <div>
                    <label className="block text-sm font-medium text-gray-700">موعد الرحلة</label>
                    <div className="mt-1">{selectedPassenger.departureTime}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">نوع السكن</label>
                    <div className="mt-1">{getAccommodationTypeLabel(selectedPassenger.accommodationType)}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">رقم المقعد</label>
                    <div className="mt-1 font-medium text-emerald-600">{selectedPassenger.seatNumber}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">طريقة الدفع</label>
                    <div className="mt-1">{getPaymentMethodLabel(selectedPassenger.paymentMethod)}</div>
                  </div>
                </div>
              </div>

              {/* Financial Info */}
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-800 mb-3">المعلومات المالية</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <span className="text-sm text-gray-600">المبلغ الإجمالي:</span>
                    <div className="font-bold text-green-600">{selectedPassenger.totalAmount.toLocaleString()} ريال</div>
                  </div>
                  <div>
                    <span className="text-sm text-gray-600">المبلغ المدفوع:</span>
                    <div className="font-bold text-blue-600">{selectedPassenger.paidAmount.toLocaleString()} ريال</div>
                  </div>
                  <div>
                    <span className="text-sm text-gray-600">المبلغ المتبقي:</span>
                    <div className="font-bold text-red-600">{selectedPassenger.remainingAmount.toLocaleString()} ريال</div>
                  </div>
                </div>
              </div>

              {/* Companions */}
              {selectedPassenger.companions && selectedPassenger.companions.length > 0 && (
                <div className="mt-6">
                  <h4 className="font-medium text-gray-800 mb-3">المرافقون ({selectedPassenger.companions.length})</h4>
                  <div className="space-y-2">
                    {selectedPassenger.companions.map((companion, index) => (
                      <div key={index} className="p-3 bg-gray-50 rounded-lg">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                          <div><strong>الاسم:</strong> {companion.name}</div>
                          <div><strong>رقم الهوية:</strong> {companion.idNumber}</div>
                          <div><strong>الجنسية:</strong> {companion.nationality}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex justify-end space-x-2 space-x-reverse mt-6 pt-4 border-t">
                <button
                  onClick={() => handlePrint(selectedPassenger)}
                  className="flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Printer className="h-4 w-4" />
                  <span>طباعة</span>
                </button>
                <button
                  onClick={() => handleExportPDF(selectedPassenger)}
                  className="flex items-center space-x-2 space-x-reverse bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
                >
                  <Download className="h-4 w-4" />
                  <span>تصدير PDF</span>
                </button>
                <button
                  onClick={() => setShowViewModal(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Edit Modal */}
        {showEditModal && selectedPassenger && (
          <EditPassengerModal
            passenger={selectedPassenger}
            onSave={handleSaveEdit}
            onCancel={() => {
              setShowEditModal(false);
              setSelectedPassenger(null);
            }}
          />
        )}
      </div>
    </div>
  );
};

// Edit Passenger Modal Component
interface EditPassengerModalProps {
  passenger: StoredTicket;
  onSave: (passenger: StoredTicket) => void;
  onCancel: () => void;
}

const EditPassengerModal: React.FC<EditPassengerModalProps> = ({ passenger, onSave, onCancel }) => {
  const [editData, setEditData] = useState<StoredTicket>(passenger);

  const handleInputChange = (field: keyof StoredTicket, value: any) => {
    setEditData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(editData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl p-8 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-gray-800">تعديل بيانات المسافر</h3>
          <button onClick={onCancel} className="text-gray-500 hover:text-gray-700">✕</button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">اسم المسافر</label>
              <input
                type="text"
                value={editData.passengerName}
                onChange={(e) => handleInputChange('passengerName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الاسم بالإنجليزية</label>
              <input
                type="text"
                value={editData.passengerNameEn}
                onChange={(e) => handleInputChange('passengerNameEn', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                dir="ltr"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">رقم الجوال</label>
              <input
                type="tel"
                value={editData.phoneNumber}
                onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهوية</label>
              <input
                type="text"
                value={editData.idNumber}
                onChange={(e) => handleInputChange('idNumber', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ الذهاب</label>
              <input
                type="date"
                value={editData.departureDate}
                onChange={(e) => handleInputChange('departureDate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">موعد الرحلة</label>
              <input
                type="time"
                value={editData.departureTime}
                onChange={(e) => handleInputChange('departureTime', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">المبلغ الإجمالي</label>
              <input
                type="number"
                value={editData.totalAmount}
                onChange={(e) => handleInputChange('totalAmount', Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">المبلغ المدفوع</label>
              <input
                type="number"
                value={editData.paidAmount}
                onChange={(e) => {
                  const paid = Number(e.target.value);
                  handleInputChange('paidAmount', paid);
                  handleInputChange('remainingAmount', editData.totalAmount - paid);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-2 space-x-reverse mt-6 pt-4 border-t">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              إلغاء
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
            >
              حفظ التعديلات
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
import React, { useState, useEffect } from 'react';
import { Home, Users, User, Baby, BedDouble, Plus, UserPlus, AlertCircle, CheckCircle, Trash2 } from 'lucide-react';
import { getRooms, getTickets, saveRoom, StoredTicket, StoredRoom } from '../utils/storage';

interface Room {
  id: string;
  type: 'individual' | 'shared' | 'family';
  capacity: number;
  occupied: number;
  guests: string[];
}

export const AccommodationManagement: React.FC = () => {
  const [rooms, setRooms] = useState<Room[]>([]);
  const [tickets, setTickets] = useState<StoredTicket[]>([]);
  const [selectedType, setSelectedType] = useState<'all' | 'individual' | 'shared' | 'family'>('all');
  const [showAddRoom, setShowAddRoom] = useState(false);
  const [newRoom, setNewRoom] = useState({
    id: '',
    type: 'shared' as 'individual' | 'shared' | 'family',
    capacity: 4
  });

  // Load data
  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    const storedRooms = getRooms();
    const storedTickets = getTickets();

    const roomsWithGuests = storedRooms.map(room => {
      const guestNames = room.guests.map(ticketId => {
        const ticket = storedTickets.find(t => t.id === ticketId);
        return ticket ? ticket.passengerName : 'غير معروف';
      });

      return {
        id: room.id,
        type: room.type,
        capacity: room.capacity,
        occupied: room.occupied,
        guests: guestNames
      };
    });

    setRooms(roomsWithGuests);
    setTickets(storedTickets);
  };

  // Get passengers who need accommodation
  const getPassengersNeedingAccommodation = () => {
    return tickets.filter(ticket =>
      ticket.accommodationType !== 'none' &&
      !rooms.some(room => room.guests.includes(ticket.id))
    );
  };

  // Add passenger to room
  const addPassengerToRoom = (roomId: string, ticketId: string) => {
    const storedRooms = getRooms();
    const roomIndex = storedRooms.findIndex(r => r.id === roomId);
    const ticket = tickets.find(t => t.id === ticketId);

    if (roomIndex >= 0 && storedRooms[roomIndex].occupied < storedRooms[roomIndex].capacity) {
      // Check if passenger is already in a room
      const isAlreadyAssigned = storedRooms.some(room => room.guests.includes(ticketId));
      if (isAlreadyAssigned) {
        alert('هذا المسافر مُسكن بالفعل في غرفة أخرى');
        return;
      }

      storedRooms[roomIndex].guests.push(ticketId);
      storedRooms[roomIndex].occupied += 1;
      saveRoom(storedRooms[roomIndex]);

      // Show success message
      alert(`تم إضافة ${ticket?.passengerName || 'المسافر'} إلى غرفة ${roomId} بنجاح`);

      loadData();
    } else {
      alert('الغرفة ممتلئة أو غير موجودة');
    }
  };

  // Remove passenger from room
  const removePassengerFromRoom = (roomId: string, ticketId: string) => {
    if (window.confirm('هل أنت متأكد من إزالة هذا المسافر من الغرفة؟')) {
      const storedRooms = getRooms();
      const roomIndex = storedRooms.findIndex(r => r.id === roomId);

      if (roomIndex >= 0) {
        const guestIndex = storedRooms[roomIndex].guests.indexOf(ticketId);
        if (guestIndex >= 0) {
          storedRooms[roomIndex].guests.splice(guestIndex, 1);
          storedRooms[roomIndex].occupied -= 1;
          saveRoom(storedRooms[roomIndex]);
          loadData();
          alert('تم إزالة المسافر من الغرفة بنجاح');
        }
      }
    }
  };

  // Add new room
  const addNewRoom = () => {
    if (!newRoom.id) {
      alert('يرجى إدخال رقم الغرفة');
      return;
    }

    const storedRooms = getRooms();
    if (storedRooms.some(room => room.id === newRoom.id)) {
      alert('رقم الغرفة موجود بالفعل');
      return;
    }

    const roomToAdd: StoredRoom = {
      id: newRoom.id,
      type: newRoom.type,
      capacity: newRoom.capacity,
      occupied: 0,
      guests: []
    };

    saveRoom(roomToAdd);
    loadData();
    setShowAddRoom(false);
    setNewRoom({ id: '', type: 'shared', capacity: 4 });
    alert(`تم إضافة غرفة ${newRoom.id} بنجاح`);
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'individual':
        return <User className="h-5 w-5" />;
      case 'shared':
        return <Users className="h-5 w-5" />;
      case 'family':
        return <Baby className="h-5 w-5" />;
      default:
        return <Home className="h-5 w-5" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'individual':
        return 'فردي';
      case 'shared':
        return 'مشترك';
      case 'family':
        return 'عوائل';
      default:
        return 'غير محدد';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'individual':
        return 'bg-blue-100 text-blue-800';
      case 'shared':
        return 'bg-green-100 text-green-800';
      case 'family':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredRooms = selectedType === 'all' 
    ? rooms 
    : rooms.filter(room => room.type === selectedType);

  const stats = {
    total: rooms.length,
    occupied: rooms.filter(room => room.occupied > 0).length,
    available: rooms.filter(room => room.occupied === 0).length,
    capacity: rooms.reduce((sum, room) => sum + room.capacity, 0),
    currentGuests: rooms.reduce((sum, room) => sum + room.occupied, 0),
  };

  return (
    <div className="max-w-7xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-800 mb-8 flex items-center space-x-2 space-x-reverse">
          <Home className="h-6 w-6" />
          <span>إدارة السكن</span>
        </h2>

        {/* Passengers needing accommodation */}
        {getPassengersNeedingAccommodation().length > 0 && (
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-6 mb-8">
            <div className="flex items-center space-x-2 space-x-reverse mb-4">
              <AlertCircle className="h-5 w-5 text-orange-600" />
              <h3 className="text-lg font-semibold text-orange-800">
                مسافرون يحتاجون إلى سكن ({getPassengersNeedingAccommodation().length})
              </h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {getPassengersNeedingAccommodation().map((ticket) => (
                <div key={ticket.id} className="bg-white border border-orange-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-gray-900">{ticket.passengerName}</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      ticket.accommodationType === 'individual' ? 'bg-blue-100 text-blue-800' :
                      ticket.accommodationType === 'shared' ? 'bg-green-100 text-green-800' :
                      'bg-purple-100 text-purple-800'
                    }`}>
                      {getTypeLabel(ticket.accommodationType)}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600 mb-3">
                    <div>رقم الرحلة: {ticket.tripNumber}</div>
                    <div>المرافقون: {ticket.companions.length}</div>
                  </div>
                  <select
                    onChange={(e) => {
                      if (e.target.value) {
                        addPassengerToRoom(e.target.value, ticket.id);
                      }
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="">اختر الغرفة</option>
                    {rooms
                      .filter(room =>
                        room.type === ticket.accommodationType &&
                        room.occupied < room.capacity
                      )
                      .map(room => (
                        <option key={room.id} value={room.id}>
                          غرفة {room.id} - متاح {room.capacity - room.occupied} مقاعد
                        </option>
                      ))
                    }
                  </select>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-lg">
            <div className="text-2xl font-bold">{stats.total}</div>
            <div className="text-blue-100">إجمالي الغرف</div>
          </div>
          <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-lg">
            <div className="text-2xl font-bold">{stats.occupied}</div>
            <div className="text-green-100">غرف مشغولة</div>
          </div>
          <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white p-4 rounded-lg">
            <div className="text-2xl font-bold">{stats.available}</div>
            <div className="text-yellow-100">غرف متاحة</div>
          </div>
          <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-lg">
            <div className="text-2xl font-bold">{stats.capacity}</div>
            <div className="text-purple-100">السعة الإجمالية</div>
          </div>
          <div className="bg-gradient-to-r from-indigo-500 to-indigo-600 text-white p-4 rounded-lg">
            <div className="text-2xl font-bold">{stats.currentGuests}</div>
            <div className="text-indigo-100">النزلاء الحاليون</div>
          </div>
        </div>

        {/* Filter Tabs and Add Room Button */}
        <div className="flex flex-wrap justify-between items-center gap-4 mb-6">
          <div className="flex flex-wrap gap-2">
            {['all', 'individual', 'shared', 'family'].map((type) => (
              <button
                key={type}
                onClick={() => setSelectedType(type as any)}
                className={`flex items-center space-x-2 space-x-reverse px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                  selectedType === type
                    ? 'bg-emerald-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {getTypeIcon(type)}
                <span>
                  {type === 'all' ? 'جميع الغرف' : getTypeLabel(type)}
                </span>
              </button>
            ))}
          </div>

          <button
            onClick={() => setShowAddRoom(true)}
            className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors flex items-center space-x-2 space-x-reverse"
          >
            <Plus className="h-4 w-4" />
            <span>إضافة غرفة جديدة</span>
          </button>
        </div>

        {/* Add Room Modal */}
        {showAddRoom && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">إضافة غرفة جديدة</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">رقم الغرفة</label>
                  <input
                    type="text"
                    value={newRoom.id}
                    onChange={(e) => setNewRoom(prev => ({ ...prev, id: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    placeholder="مثال: 101"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نوع الغرفة</label>
                  <select
                    value={newRoom.type}
                    onChange={(e) => setNewRoom(prev => ({ ...prev, type: e.target.value as any }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="individual">فردي (خاص)</option>
                    <option value="shared">مشترك</option>
                    <option value="family">عوائل</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">السعة</label>
                  <input
                    type="number"
                    value={newRoom.capacity}
                    onChange={(e) => setNewRoom(prev => ({ ...prev, capacity: parseInt(e.target.value) || 1 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                    min="1"
                    max="10"
                  />
                </div>
              </div>

              <div className="flex space-x-3 space-x-reverse mt-6">
                <button
                  onClick={addNewRoom}
                  className="flex-1 bg-emerald-600 text-white py-2 px-4 rounded-lg hover:bg-emerald-700 transition-colors"
                >
                  إضافة الغرفة
                </button>
                <button
                  onClick={() => {
                    setShowAddRoom(false);
                    setNewRoom({ id: '', type: 'shared', capacity: 4 });
                  }}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Rooms Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredRooms.map((room) => (
            <div key={room.id} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <BedDouble className="h-5 w-5 text-gray-600" />
                  <span className="font-semibold text-lg">غرفة {room.id}</span>
                </div>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getTypeColor(room.type)}`}>
                  {getTypeLabel(room.type)}
                </span>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">السعة:</span>
                  <span className="font-medium">{room.capacity} أشخاص</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">المشغول:</span>
                  <span className="font-medium">{room.occupied} أشخاص</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">المتاح:</span>
                  <span className="font-medium text-emerald-600">{room.capacity - room.occupied} أشخاص</span>
                </div>
              </div>

              {/* Occupancy Bar */}
              <div className="mt-4">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      room.occupied === 0 ? 'bg-gray-400' :
                      room.occupied === room.capacity ? 'bg-red-500' : 'bg-emerald-500'
                    }`}
                    style={{ width: `${(room.occupied / room.capacity) * 100}%` }}
                  ></div>
                </div>
              </div>

              {/* Guests List */}
              {room.guests.length > 0 && (
                <div className="mt-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">النزلاء ({room.guests.length}):</h4>
                  <div className="space-y-2">
                    {room.guests.map((guestName, index) => {
                      // Find the ticket ID for this guest name
                      const ticket = tickets.find(t => t.passengerName === guestName);
                      const ticketId = ticket?.id;

                      return (
                        <div key={index} className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded-lg border">
                          <div className="flex-1">
                            <div className="text-sm font-medium text-gray-800">{guestName}</div>
                            {ticket && (
                              <div className="text-xs text-gray-600">
                                رقم الرحلة: {ticket.tripNumber} | الهوية: {ticket.idNumber}
                              </div>
                            )}
                          </div>
                          {ticketId && (
                            <button
                              onClick={() => removePassengerFromRoom(room.id, ticketId)}
                              className="text-red-600 hover:text-red-800 transition-colors p-1 ml-2"
                              title="إزالة من الغرفة"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Add Passenger to Room */}
              {room.occupied < room.capacity && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <label className="block text-sm font-medium text-gray-700 mb-2">إضافة مسافر إلى الغرفة:</label>
                  <select
                    onChange={(e) => {
                      if (e.target.value) {
                        addPassengerToRoom(room.id, e.target.value);
                        e.target.value = ''; // Reset selection
                      }
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="">اختر مسافر</option>
                    {getPassengersNeedingAccommodation()
                      .filter(ticket => ticket.accommodationType === room.type)
                      .map(ticket => (
                        <option key={ticket.id} value={ticket.id}>
                          {ticket.passengerName} - {ticket.tripNumber}
                        </option>
                      ))
                    }
                  </select>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
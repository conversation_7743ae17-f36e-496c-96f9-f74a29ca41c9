import React, { useState } from 'react';
import { Home, Users, User, Baby, BedDouble } from 'lucide-react';
import { getRooms, getTickets } from '../utils/storage';

interface Room {
  id: string;
  type: 'individual' | 'shared' | 'family';
  capacity: number;
  occupied: number;
  guests: string[];
}

export const AccommodationManagement: React.FC = () => {
  const [rooms, setRooms] = useState<Room[]>(() => {
    const storedRooms = getRooms();
    const tickets = getTickets();
    
    return storedRooms.map(room => {
      const guestNames = room.guests.map(ticketId => {
        const ticket = tickets.find(t => t.id === ticketId);
        return ticket ? ticket.passengerName : 'غير معروف';
      });
      
      return {
        id: room.id,
        type: room.type,
        capacity: room.capacity,
        occupied: room.occupied,
        guests: guestNames
      };
    });
  });

  const [selectedType, setSelectedType] = useState<'all' | 'individual' | 'shared' | 'family'>('all');

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'individual':
        return <User className="h-5 w-5" />;
      case 'shared':
        return <Users className="h-5 w-5" />;
      case 'family':
        return <Baby className="h-5 w-5" />;
      default:
        return <Home className="h-5 w-5" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'individual':
        return 'فردي';
      case 'shared':
        return 'مشترك';
      case 'family':
        return 'عوائل';
      default:
        return 'غير محدد';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'individual':
        return 'bg-blue-100 text-blue-800';
      case 'shared':
        return 'bg-green-100 text-green-800';
      case 'family':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredRooms = selectedType === 'all' 
    ? rooms 
    : rooms.filter(room => room.type === selectedType);

  const stats = {
    total: rooms.length,
    occupied: rooms.filter(room => room.occupied > 0).length,
    available: rooms.filter(room => room.occupied === 0).length,
    capacity: rooms.reduce((sum, room) => sum + room.capacity, 0),
    currentGuests: rooms.reduce((sum, room) => sum + room.occupied, 0),
  };

  return (
    <div className="max-w-7xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-800 mb-8 flex items-center space-x-2 space-x-reverse">
          <Home className="h-6 w-6" />
          <span>إدارة السكن</span>
        </h2>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-lg">
            <div className="text-2xl font-bold">{stats.total}</div>
            <div className="text-blue-100">إجمالي الغرف</div>
          </div>
          <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-lg">
            <div className="text-2xl font-bold">{stats.occupied}</div>
            <div className="text-green-100">غرف مشغولة</div>
          </div>
          <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white p-4 rounded-lg">
            <div className="text-2xl font-bold">{stats.available}</div>
            <div className="text-yellow-100">غرف متاحة</div>
          </div>
          <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-lg">
            <div className="text-2xl font-bold">{stats.capacity}</div>
            <div className="text-purple-100">السعة الإجمالية</div>
          </div>
          <div className="bg-gradient-to-r from-indigo-500 to-indigo-600 text-white p-4 rounded-lg">
            <div className="text-2xl font-bold">{stats.currentGuests}</div>
            <div className="text-indigo-100">النزلاء الحاليون</div>
          </div>
        </div>

        {/* Filter Tabs */}
        <div className="flex flex-wrap gap-2 mb-6">
          {['all', 'individual', 'shared', 'family'].map((type) => (
            <button
              key={type}
              onClick={() => setSelectedType(type as any)}
              className={`flex items-center space-x-2 space-x-reverse px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                selectedType === type
                  ? 'bg-emerald-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {getTypeIcon(type)}
              <span>
                {type === 'all' ? 'جميع الغرف' : getTypeLabel(type)}
              </span>
            </button>
          ))}
        </div>

        {/* Rooms Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredRooms.map((room) => (
            <div key={room.id} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <BedDouble className="h-5 w-5 text-gray-600" />
                  <span className="font-semibold text-lg">غرفة {room.id}</span>
                </div>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getTypeColor(room.type)}`}>
                  {getTypeLabel(room.type)}
                </span>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">السعة:</span>
                  <span className="font-medium">{room.capacity} أشخاص</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">المشغول:</span>
                  <span className="font-medium">{room.occupied} أشخاص</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">المتاح:</span>
                  <span className="font-medium text-emerald-600">{room.capacity - room.occupied} أشخاص</span>
                </div>
              </div>

              {/* Occupancy Bar */}
              <div className="mt-4">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      room.occupied === 0 ? 'bg-gray-400' :
                      room.occupied === room.capacity ? 'bg-red-500' : 'bg-emerald-500'
                    }`}
                    style={{ width: `${(room.occupied / room.capacity) * 100}%` }}
                  ></div>
                </div>
              </div>

              {/* Guests List */}
              {room.guests.length > 0 && (
                <div className="mt-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">النزلاء:</h4>
                  <div className="space-y-1">
                    {room.guests.map((guest, index) => (
                      <div key={index} className="text-sm text-gray-600 bg-gray-50 px-2 py-1 rounded">
                        {guest}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-2 space-x-reverse mt-4 pt-4 border-t border-gray-200">
                <button className="flex-1 bg-emerald-600 text-white py-2 px-3 rounded-lg hover:bg-emerald-700 transition-colors text-sm">
                  إدارة الغرفة
                </button>
                <button className="flex-1 bg-gray-100 text-gray-700 py-2 px-3 rounded-lg hover:bg-gray-200 transition-colors text-sm">
                  عرض التفاصيل
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
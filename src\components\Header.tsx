import React from 'react';
import { MapPin, Users, Calendar, Trash2 } from 'lucide-react';
import { clearAllData } from '../utils/storage';

export const Header: React.FC = () => {
  return (
    <header className="bg-gradient-to-r from-emerald-600 to-blue-600 text-white shadow-lg">
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="bg-white/10 p-3 rounded-full backdrop-blur-sm">
              <MapPin className="h-8 w-8" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">كيان المسافر</h1>
              <p className="text-emerald-100 text-lg">نظام إدارة رحلات العمرة</p>
            </div>
          </div>
          <div className="flex items-center space-x-6 space-x-reverse">
            <div className="flex items-center space-x-2 space-x-reverse bg-white/10 px-4 py-2 rounded-full backdrop-blur-sm">
              <Users className="h-5 w-5" />
              <span className="font-medium">المسافرون</span>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse bg-white/10 px-4 py-2 rounded-full backdrop-blur-sm">
              <Calendar className="h-5 w-5" />
              <span className="font-medium">{new Date().toLocaleDateString('ar-SA')}</span>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};
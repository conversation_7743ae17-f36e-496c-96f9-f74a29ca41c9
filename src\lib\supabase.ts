import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl = 'https://fxbqqerhiuelpmaksrpn.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4YnFxZXJoaXVlbHBtYWtzcnBuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5OTU1NDQsImV4cCI6MjA2NTU3MTU0NH0.jdHs2cfL0uyLrtB87cRFPEQ4CHBBZIxjQe0jJwaZ9aA'

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface Trip {
  id: string
  trip_number: string
  passenger_count: number
  max_capacity: number
  departure_date: string
  departure_time: string
  trip_type: 'one-way' | 'return' | 'round-trip'
  return_date?: string
  occupied_seats: number[]
  status: string
  created_at: string
  updated_at: string
}

export interface Ticket {
  id: string
  trip_id: string
  trip_number: string
  passenger_name: string
  passenger_name_en?: string
  nationality: string
  phone_number: string
  id_number: string
  departure_date: string
  trip_type: 'one-way' | 'return' | 'round-trip'
  return_date?: string
  departure_time?: string
  accommodation_type: 'individual' | 'shared' | 'family' | 'none'
  seat_number?: string
  payment_method: 'cash' | 'visa' | 'network'
  paid_amount: number
  remaining_amount: number
  total_amount: number
  status: string
  created_at: string
  updated_at: string
}

export interface Companion {
  id: string
  ticket_id: string
  name: string
  id_number: string
  nationality: string
  seat_number?: string
  created_at: string
  updated_at: string
}

export interface Room {
  id: string
  room_number: string
  room_type: 'individual' | 'shared' | 'family'
  capacity: number
  occupied: number
  status: string
  created_at: string
  updated_at: string
}

export interface RoomAssignment {
  id: string
  room_id: string
  ticket_id: string
  assigned_at: string
}

export interface Driver {
  id: string
  name: string
  id_number: string
  phone_number: string
  license_number?: string
  license_expiry?: string
  status: string
  created_at: string
  updated_at: string
}

export interface TripDriver {
  id: string
  trip_id: string
  driver_id: string
  assigned_at: string
}

export interface Expense {
  id: string
  expense_type: string
  category: string
  amount: number
  expense_date: string
  notes?: string
  payment_method: 'cash' | 'visa' | 'network'
  created_at: string
  updated_at: string
}

// Print and PDF utilities
export const printTicket = (ticketData: any) => {
  const printWindow = window.open('', '_blank');
  if (!printWindow) return;

  const printContent = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>تذكرة العمرة - ${ticketData.tripNumber}</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          line-height: 1.5;
          color: #333;
          background: white;
          padding: 15px;
          font-size: 14px;
        }

        .ticket-container {
          max-width: 210mm;
          margin: 0 auto;
          border: 2px solid #059669;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          page-break-inside: avoid;
        }

        .ticket-header {
          background: linear-gradient(135deg, #059669, #0284c7);
          color: white;
          padding: 25px;
          text-align: center;
        }

        .company-name {
          font-size: 28px;
          font-weight: bold;
          margin-bottom: 8px;
        }

        .ticket-title {
          font-size: 18px;
          opacity: 0.9;
        }

        .ticket-body {
          padding: 25px;
          background: white;
        }

        .trip-info {
          background: #f8fafc;
          padding: 15px;
          border-radius: 8px;
          margin-bottom: 20px;
          border-right: 4px solid #059669;
          text-align: center;
        }

        .trip-number {
          font-size: 22px;
          font-weight: bold;
          color: #059669;
          margin-bottom: 5px;
        }

        .trip-date {
          font-size: 14px;
          color: #6b7280;
        }

        .passenger-section {
          margin-bottom: 25px;
        }

        .section-title {
          font-size: 18px;
          font-weight: bold;
          color: #059669;
          margin-bottom: 15px;
          padding-bottom: 8px;
          border-bottom: 2px solid #e5e7eb;
        }

        .passenger-main {
          background: #f0fdf4;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
          border: 1px solid #bbf7d0;
        }

        .passenger-name {
          font-size: 20px;
          font-weight: bold;
          color: #065f46;
          margin-bottom: 15px;
          text-align: center;
        }

        .info-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 15px;
        }

        .info-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid #e5e7eb;
        }

        .info-label {
          font-weight: 600;
          color: #374151;
          min-width: 120px;
        }

        .info-value {
          color: #059669;
          font-weight: 500;
          text-align: left;
        }

        .companions-section {
          margin-bottom: 25px;
        }

        .companions-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 15px;
        }

        .companion-card {
          background: #fef3c7;
          padding: 15px;
          border-radius: 8px;
          border: 1px solid #fbbf24;
        }

        .companion-name {
          font-size: 16px;
          font-weight: bold;
          color: #92400e;
          margin-bottom: 8px;
        }

        .companion-details {
          font-size: 13px;
          color: #78350f;
        }

        .companion-detail {
          margin-bottom: 4px;
        }

        .financial-summary {
          background: #ecfdf5;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
          border: 1px solid #d1fae5;
        }

        .financial-title {
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 15px;
          color: #065f46;
        }

        .financial-grid {
          display: grid;
          grid-template-columns: 1fr 1fr 1fr;
          gap: 15px;
        }

        .financial-item {
          text-align: center;
          padding: 10px;
          background: white;
          border-radius: 6px;
          border: 1px solid #d1fae5;
        }

        .financial-label {
          font-size: 12px;
          color: #6b7280;
          margin-bottom: 5px;
        }

        .financial-value {
          font-size: 16px;
          font-weight: bold;
          color: #065f46;
        }

        .ticket-footer {
          background: #f8fafc;
          padding: 15px;
          text-align: center;
          border-top: 1px solid #e5e7eb;
        }

        .footer-text {
          color: #6b7280;
          font-size: 13px;
          margin-bottom: 8px;
        }

        .print-date {
          font-size: 11px;
          color: #9ca3af;
        }

        @media print {
          body {
            padding: 0;
            font-size: 12px;
          }

          .ticket-container {
            box-shadow: none;
            border: 1px solid #ccc;
            max-width: 100%;
          }

          .ticket-header {
            padding: 20px;
          }

          .company-name {
            font-size: 24px;
          }

          .ticket-title {
            font-size: 16px;
          }

          .ticket-body {
            padding: 20px;
          }

          .passenger-name {
            font-size: 18px;
          }

          .trip-number {
            font-size: 20px;
          }

          .companions-grid {
            grid-template-columns: 1fr 1fr;
          }

          .financial-grid {
            grid-template-columns: 1fr 1fr 1fr;
          }
        }
      </style>
    </head>
    <body>
      <div class="ticket-container">
        <div class="ticket-header">
          <div class="company-name">كيان المسافر</div>
          <div class="ticket-title">تذكرة العمرة</div>
        </div>

        <div class="ticket-body">
          <!-- معلومات الرحلة -->
          <div class="trip-info">
            <div class="trip-number">رقم الرحلة: ${ticketData.tripNumber}</div>
            <div class="trip-date">تاريخ الإصدار: ${new Date().toLocaleDateString('ar-SA')}</div>
          </div>

          <!-- بيانات المسافر الرئيسي -->
          <div class="passenger-section">
            <div class="section-title">بيانات المسافر الرئيسي</div>
            <div class="passenger-main">
              <div class="passenger-name">${ticketData.passengerName}</div>
              <div class="info-grid">
                <div>
                  <div class="info-item">
                    <span class="info-label">الاسم بالإنجليزية:</span>
                    <span class="info-value">${ticketData.passengerNameEn || 'غير محدد'}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">رقم الهوية:</span>
                    <span class="info-value">${ticketData.idNumber}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">الجنسية:</span>
                    <span class="info-value">${ticketData.nationality}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">رقم الجوال:</span>
                    <span class="info-value">${ticketData.phoneNumber}</span>
                  </div>
                </div>
                <div>
                  <div class="info-item">
                    <span class="info-label">تاريخ الذهاب:</span>
                    <span class="info-value">${new Date(ticketData.departureDate).toLocaleDateString('ar-SA')}</span>
                  </div>
                  ${ticketData.returnDate ? `
                    <div class="info-item">
                      <span class="info-label">تاريخ العودة:</span>
                      <span class="info-value">${new Date(ticketData.returnDate).toLocaleDateString('ar-SA')}</span>
                    </div>
                  ` : ''}
                  <div class="info-item">
                    <span class="info-label">موعد الرحلة:</span>
                    <span class="info-value">${ticketData.departureTime}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">نوع الرحلة:</span>
                    <span class="info-value">${
                      ticketData.tripType === 'one-way' ? 'ذهاب فقط' :
                      ticketData.tripType === 'return' ? 'عودة فقط' : 'ذهاب وعودة'
                    }</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">نوع السكن:</span>
                    <span class="info-value">${
                      ticketData.accommodationType === 'individual' ? 'فردي' :
                      ticketData.accommodationType === 'shared' ? 'مشترك' :
                      ticketData.accommodationType === 'family' ? 'عوائل' : 'بدون سكن'
                    }</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">رقم المقعد:</span>
                    <span class="info-value">${ticketData.seatNumber}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- بيانات المرافقين -->
          ${ticketData.companions && ticketData.companions.length > 0 ? `
            <div class="companions-section">
              <div class="section-title">بيانات المرافقين (${ticketData.companions.length} مرافق)</div>
              <div class="companions-grid">
                ${ticketData.companions.map((companion: any, index: number) => `
                  <div class="companion-card">
                    <div class="companion-name">المرافق ${index + 1}: ${companion.name}</div>
                    <div class="companion-details">
                      <div class="companion-detail"><strong>رقم الهوية:</strong> ${companion.idNumber}</div>
                      <div class="companion-detail"><strong>الجنسية:</strong> ${companion.nationality}</div>
                      <div class="companion-detail"><strong>رقم المقعد:</strong> ${companion.seatNumber || 'غير محدد'}</div>
                    </div>
                  </div>
                `).join('')}
              </div>
            </div>
          ` : `
            <div class="companions-section">
              <div class="section-title">المرافقين</div>
              <div style="text-align: center; padding: 20px; color: #6b7280; font-style: italic;">
                لا يوجد مرافقين مسجلين
              </div>
            </div>
          `}

          <!-- الملخص المالي -->
          <div class="financial-summary">
            <div class="financial-title">الملخص المالي</div>
            <div class="financial-grid">
              <div class="financial-item">
                <div class="financial-label">المبلغ الإجمالي</div>
                <div class="financial-value">${ticketData.totalAmount.toLocaleString()} ريال</div>
              </div>
              <div class="financial-item">
                <div class="financial-label">المبلغ المدفوع</div>
                <div class="financial-value">${ticketData.paidAmount.toLocaleString()} ريال</div>
              </div>
              <div class="financial-item">
                <div class="financial-label">المبلغ المتبقي</div>
                <div class="financial-value" style="color: ${ticketData.remainingAmount > 0 ? '#dc2626' : '#059669'};">
                  ${ticketData.remainingAmount.toLocaleString()} ريال
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="ticket-footer">
          <div class="footer-text">
            شكراً لاختياركم كيان المسافر لخدمات العمرة - نتمنى لكم رحلة مباركة
          </div>
          <div class="print-date">
            تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')} - ${new Date().toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })}
          </div>
        </div>
      </div>
    </body>
    </html>
  `;

  printWindow.document.write(printContent);
  printWindow.document.close();
  printWindow.focus();
  
  setTimeout(() => {
    printWindow.print();
    printWindow.close();
  }, 500);
};

export const exportToPDF = async (ticketData: any) => {
  // Use the same enhanced design for PDF export
  printTicket(ticketData);
};

// دالة لطباعة كشف شامل للرحلة مع جميع المسافرين والمرافقين
export const printTripManifest = (tripData: any) => {
  const printWindow = window.open('', '_blank');
  if (!printWindow) return;

  const totalPassengers = tripData.passengers.reduce((total: number, passenger: any) => {
    return total + 1 + (passenger.companions ? passenger.companions.length : 0);
  }, 0);

  const printContent = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>كشف الرحلة - ${tripData.tripNumber}</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          line-height: 1.4;
          color: #333;
          background: white;
          padding: 15px;
          font-size: 12px;
        }

        .manifest-container {
          max-width: 210mm;
          margin: 0 auto;
        }

        .manifest-header {
          background: linear-gradient(135deg, #059669, #0284c7);
          color: white;
          padding: 20px;
          text-align: center;
          border-radius: 8px;
          margin-bottom: 20px;
        }

        .company-name {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 5px;
        }

        .manifest-title {
          font-size: 16px;
          opacity: 0.9;
        }

        .trip-summary {
          background: #f8fafc;
          padding: 15px;
          border-radius: 8px;
          margin-bottom: 20px;
          border-right: 4px solid #059669;
        }

        .summary-grid {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 15px;
          text-align: center;
        }

        .summary-item {
          background: white;
          padding: 10px;
          border-radius: 6px;
          border: 1px solid #e5e7eb;
        }

        .summary-label {
          font-size: 11px;
          color: #6b7280;
          margin-bottom: 5px;
        }

        .summary-value {
          font-size: 16px;
          font-weight: bold;
          color: #059669;
        }

        .passengers-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
          background: white;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .passengers-table th {
          background: #059669;
          color: white;
          padding: 12px 8px;
          text-align: center;
          font-weight: bold;
          font-size: 11px;
        }

        .passengers-table td {
          padding: 10px 8px;
          border-bottom: 1px solid #e5e7eb;
          text-align: center;
          font-size: 11px;
        }

        .passengers-table tr:nth-child(even) {
          background: #f9fafb;
        }

        .passenger-main {
          background: #f0fdf4;
          font-weight: bold;
        }

        .companion-row {
          background: #fef3c7;
          font-style: italic;
        }

        .manifest-footer {
          background: #f8fafc;
          padding: 15px;
          text-align: center;
          border-radius: 8px;
          border: 1px solid #e5e7eb;
        }

        .footer-text {
          color: #6b7280;
          font-size: 11px;
          margin-bottom: 8px;
        }

        .print-date {
          font-size: 10px;
          color: #9ca3af;
        }

        @media print {
          body {
            padding: 0;
            font-size: 10px;
          }

          .manifest-header {
            padding: 15px;
          }

          .company-name {
            font-size: 20px;
          }

          .manifest-title {
            font-size: 14px;
          }

          .passengers-table th,
          .passengers-table td {
            padding: 6px 4px;
            font-size: 9px;
          }

          .summary-grid {
            grid-template-columns: repeat(4, 1fr);
          }
        }
      </style>
    </head>
    <body>
      <div class="manifest-container">
        <div class="manifest-header">
          <div class="company-name">كيان المسافر</div>
          <div class="manifest-title">كشف شامل للرحلة</div>
        </div>

        <div class="trip-summary">
          <div class="summary-grid">
            <div class="summary-item">
              <div class="summary-label">رقم الرحلة</div>
              <div class="summary-value">${tripData.tripNumber}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">إجمالي المسافرين</div>
              <div class="summary-value">${totalPassengers}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">تاريخ الرحلة</div>
              <div class="summary-value">${tripData.departureDate ? new Date(tripData.departureDate).toLocaleDateString('ar-SA') : 'غير محدد'}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">سعة الباص</div>
              <div class="summary-value">${tripData.maxCapacity || 50}</div>
            </div>
          </div>
        </div>

        <table class="passengers-table">
          <thead>
            <tr>
              <th style="width: 5%;">#</th>
              <th style="width: 25%;">اسم المسافر</th>
              <th style="width: 15%;">رقم الهوية</th>
              <th style="width: 10%;">الجنسية</th>
              <th style="width: 15%;">رقم الجوال</th>
              <th style="width: 8%;">المقعد</th>
              <th style="width: 12%;">نوع السكن</th>
              <th style="width: 10%;">الحالة</th>
            </tr>
          </thead>
          <tbody>
            ${tripData.passengers.map((passenger: any, index: number) => {
              let rows = '';

              // المسافر الرئيسي
              rows += `
                <tr class="passenger-main">
                  <td>${index + 1}</td>
                  <td><strong>${passenger.passengerName}</strong></td>
                  <td>${passenger.idNumber}</td>
                  <td>${passenger.nationality}</td>
                  <td>${passenger.phoneNumber}</td>
                  <td>${passenger.seatNumber}</td>
                  <td>${
                    passenger.accommodationType === 'individual' ? 'فردي' :
                    passenger.accommodationType === 'shared' ? 'مشترك' :
                    passenger.accommodationType === 'family' ? 'عوائل' : 'بدون سكن'
                  }</td>
                  <td>مسافر رئيسي</td>
                </tr>
              `;

              // المرافقين
              if (passenger.companions && passenger.companions.length > 0) {
                passenger.companions.forEach((companion: any, compIndex: number) => {
                  rows += `
                    <tr class="companion-row">
                      <td>-</td>
                      <td>${companion.name} (مرافق)</td>
                      <td>${companion.idNumber}</td>
                      <td>${companion.nationality}</td>
                      <td>-</td>
                      <td>${companion.seatNumber || 'غير محدد'}</td>
                      <td>مع المسافر الرئيسي</td>
                      <td>مرافق ${compIndex + 1}</td>
                    </tr>
                  `;
                });
              }

              return rows;
            }).join('')}
          </tbody>
        </table>

        <div class="manifest-footer">
          <div class="footer-text">
            كيان المسافر - خدمات العمرة | إجمالي المسافرين: ${totalPassengers} | تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}
          </div>
          <div class="print-date">
            تم إنشاء هذا الكشف في: ${new Date().toLocaleString('ar-SA')}
          </div>
        </div>
      </div>
    </body>
    </html>
  `;

  printWindow.document.write(printContent);
  printWindow.document.close();
  printWindow.focus();

  setTimeout(() => {
    printWindow.print();
    printWindow.close();
  }, 500);
};
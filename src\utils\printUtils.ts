// Print and PDF utilities
export const printTicket = (ticketData: any) => {
  const printWindow = window.open('', '_blank');
  if (!printWindow) return;

  const printContent = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>تذكرة العمرة - ${ticketData.tripNumber}</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          line-height: 1.6;
          color: #333;
          background: white;
          padding: 20px;
        }
        
        .ticket-container {
          max-width: 800px;
          margin: 0 auto;
          border: 2px solid #059669;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .ticket-header {
          background: linear-gradient(135deg, #059669, #0284c7);
          color: white;
          padding: 30px;
          text-align: center;
        }
        
        .company-name {
          font-size: 32px;
          font-weight: bold;
          margin-bottom: 10px;
        }
        
        .ticket-title {
          font-size: 20px;
          opacity: 0.9;
        }
        
        .ticket-body {
          padding: 30px;
          background: white;
        }
        
        .trip-info {
          background: #f8fafc;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 30px;
          border-left: 4px solid #059669;
        }
        
        .trip-number {
          font-size: 24px;
          font-weight: bold;
          color: #059669;
          text-align: center;
          margin-bottom: 15px;
        }
        
        .info-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 20px;
          margin-bottom: 30px;
        }
        
        .info-item {
          display: flex;
          justify-content: space-between;
          padding: 12px 0;
          border-bottom: 1px solid #e5e7eb;
        }
        
        .info-label {
          font-weight: 600;
          color: #374151;
        }
        
        .info-value {
          color: #059669;
          font-weight: 500;
        }
        
        .companions-section {
          margin-top: 30px;
          padding: 20px;
          background: #f9fafb;
          border-radius: 8px;
        }
        
        .companions-title {
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 15px;
          color: #374151;
        }
        
        .companion-item {
          padding: 10px;
          background: white;
          margin-bottom: 8px;
          border-radius: 6px;
          border: 1px solid #e5e7eb;
        }
        
        .financial-summary {
          background: #ecfdf5;
          padding: 20px;
          border-radius: 8px;
          margin-top: 30px;
          border: 1px solid #d1fae5;
        }
        
        .financial-title {
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 15px;
          color: #065f46;
        }
        
        .financial-item {
          display: flex;
          justify-content: space-between;
          padding: 8px 0;
          border-bottom: 1px solid #d1fae5;
        }
        
        .financial-item:last-child {
          border-bottom: none;
          font-weight: bold;
          font-size: 16px;
        }
        
        .ticket-footer {
          background: #f8fafc;
          padding: 20px;
          text-align: center;
          border-top: 1px solid #e5e7eb;
        }
        
        .footer-text {
          color: #6b7280;
          font-size: 14px;
        }
        
        .print-date {
          margin-top: 10px;
          font-size: 12px;
          color: #9ca3af;
        }
        
        @media print {
          body {
            padding: 0;
          }
          
          .ticket-container {
            box-shadow: none;
            border: 1px solid #ccc;
          }
        }
      </style>
    </head>
    <body>
      <div class="ticket-container">
        <div class="ticket-header">
          <div class="company-name">كيان المسافر</div>
          <div class="ticket-title">تذكرة العمرة</div>
        </div>
        
        <div class="ticket-body">
          <div class="trip-info">
            <div class="trip-number">رقم الرحلة: ${ticketData.tripNumber}</div>
          </div>
          
          <div class="info-grid">
            <div>
              <div class="info-item">
                <span class="info-label">اسم المسافر:</span>
                <span class="info-value">${ticketData.passengerName}</span>
              </div>
              <div class="info-item">
                <span class="info-label">الاسم بالإنجليزية:</span>
                <span class="info-value">${ticketData.passengerNameEn}</span>
              </div>
              <div class="info-item">
                <span class="info-label">الجنسية:</span>
                <span class="info-value">${ticketData.nationality}</span>
              </div>
              <div class="info-item">
                <span class="info-label">رقم الهوية:</span>
                <span class="info-value">${ticketData.idNumber}</span>
              </div>
              <div class="info-item">
                <span class="info-label">رقم الجوال:</span>
                <span class="info-value">${ticketData.phoneNumber}</span>
              </div>
            </div>
            
            <div>
              <div class="info-item">
                <span class="info-label">تاريخ الذهاب:</span>
                <span class="info-value">${new Date(ticketData.departureDate).toLocaleDateString('ar-SA')}</span>
              </div>
              ${ticketData.returnDate ? `
                <div class="info-item">
                  <span class="info-label">تاريخ العودة:</span>
                  <span class="info-value">${new Date(ticketData.returnDate).toLocaleDateString('ar-SA')}</span>
                </div>
              ` : ''}
              <div class="info-item">
                <span class="info-label">موعد الرحلة:</span>
                <span class="info-value">${ticketData.departureTime}</span>
              </div>
              <div class="info-item">
                <span class="info-label">نوع الرحلة:</span>
                <span class="info-value">${
                  ticketData.tripType === 'one-way' ? 'ذهاب فقط' :
                  ticketData.tripType === 'return' ? 'عودة فقط' : 'ذهاب وعودة'
                }</span>
              </div>
              <div class="info-item">
                <span class="info-label">نوع السكن:</span>
                <span class="info-value">${
                  ticketData.accommodationType === 'individual' ? 'فردي' :
                  ticketData.accommodationType === 'shared' ? 'مشترك' :
                  ticketData.accommodationType === 'family' ? 'عوائل' : 'بدون سكن'
                }</span>
              </div>
              <div class="info-item">
                <span class="info-label">رقم المقعد:</span>
                <span class="info-value">${ticketData.seatNumber}</span>
              </div>
            </div>
          </div>
          
          ${ticketData.companions && ticketData.companions.length > 0 ? `
            <div class="companions-section">
              <div class="companions-title">المرافقون (${ticketData.companions.length})</div>
              ${ticketData.companions.map((companion: any) => `
                <div class="companion-item">
                  <strong>${companion.name}</strong> - ${companion.idNumber} - ${companion.nationality}
                </div>
              `).join('')}
            </div>
          ` : ''}
          
          <div class="financial-summary">
            <div class="financial-title">الملخص المالي</div>
            <div class="financial-item">
              <span>المبلغ الإجمالي:</span>
              <span>${ticketData.totalAmount.toLocaleString()} ريال</span>
            </div>
            <div class="financial-item">
              <span>المبلغ المدفوع:</span>
              <span>${ticketData.paidAmount.toLocaleString()} ريال</span>
            </div>
            <div class="financial-item">
              <span>المبلغ المتبقي:</span>
              <span>${ticketData.remainingAmount.toLocaleString()} ريال</span>
            </div>
          </div>
        </div>
        
        <div class="ticket-footer">
          <div class="footer-text">
            شكراً لاختياركم كيان المسافر لخدمات العمرة
          </div>
          <div class="print-date">
            تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')} - ${new Date().toLocaleTimeString('ar-SA')}
          </div>
        </div>
      </div>
    </body>
    </html>
  `;

  printWindow.document.write(printContent);
  printWindow.document.close();
  printWindow.focus();
  
  setTimeout(() => {
    printWindow.print();
    printWindow.close();
  }, 500);
};

export const exportToPDF = async (ticketData: any) => {
  // Create a temporary window for PDF generation
  const printWindow = window.open('', '_blank');
  if (!printWindow) return;

  const content = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <title>تذكرة العمرة - ${ticketData.tripNumber}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .ticket { border: 2px solid #059669; border-radius: 10px; padding: 20px; }
        .header { background: #059669; color: white; padding: 20px; text-align: center; margin: -20px -20px 20px -20px; }
        .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .info-item { margin: 10px 0; }
        .label { font-weight: bold; }
        .value { color: #059669; }
      </style>
    </head>
    <body>
      <div class="ticket">
        <div class="header">
          <h1>كيان المسافر</h1>
          <h2>تذكرة العمرة</h2>
        </div>
        <h3>رقم الرحلة: ${ticketData.tripNumber}</h3>
        <div class="info-grid">
          <div>
            <div class="info-item"><span class="label">اسم المسافر:</span> <span class="value">${ticketData.passengerName}</span></div>
            <div class="info-item"><span class="label">رقم الهوية:</span> <span class="value">${ticketData.idNumber}</span></div>
            <div class="info-item"><span class="label">رقم الجوال:</span> <span class="value">${ticketData.phoneNumber}</span></div>
          </div>
          <div>
            <div class="info-item"><span class="label">تاريخ الذهاب:</span> <span class="value">${ticketData.departureDate}</span></div>
            <div class="info-item"><span class="label">رقم المقعد:</span> <span class="value">${ticketData.seatNumber}</span></div>
            <div class="info-item"><span class="label">المبلغ الإجمالي:</span> <span class="value">${ticketData.totalAmount} ريال</span></div>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;

  printWindow.document.write(content);
  printWindow.document.close();
  
  // Trigger print dialog which can save as PDF
  setTimeout(() => {
    printWindow.print();
  }, 500);
};
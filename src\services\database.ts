import { supabase, Trip, Ticket, Companion, Room, RoomAssignment, Driver, TripDriver, Expense } from '../lib/supabase'

// Trip Services
export const tripService = {
  // Get all trips
  async getAll(): Promise<Trip[]> {
    const { data, error } = await supabase
      .from('trips')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Get trip by number
  async getByNumber(tripNumber: string): Promise<Trip | null> {
    const { data, error } = await supabase
      .from('trips')
      .select('*')
      .eq('trip_number', tripNumber)
      .single()
    
    if (error && error.code !== 'PGRST116') throw error
    return data
  },

  // Create new trip
  async create(trip: Omit<Trip, 'id' | 'created_at' | 'updated_at'>): Promise<Trip> {
    const { data, error } = await supabase
      .from('trips')
      .insert(trip)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Update trip
  async update(id: string, updates: Partial<Trip>): Promise<Trip> {
    const { data, error } = await supabase
      .from('trips')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Delete trip
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('trips')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }
}

// Ticket Services
export const ticketService = {
  // Get all tickets
  async getAll(): Promise<Ticket[]> {
    const { data, error } = await supabase
      .from('tickets')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Get tickets by trip
  async getByTrip(tripId: string): Promise<Ticket[]> {
    const { data, error } = await supabase
      .from('tickets')
      .select('*')
      .eq('trip_id', tripId)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data || []
  },

  // Create new ticket
  async create(ticket: Omit<Ticket, 'id' | 'created_at' | 'updated_at'>): Promise<Ticket> {
    const { data, error } = await supabase
      .from('tickets')
      .insert(ticket)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Update ticket
  async update(id: string, updates: Partial<Ticket>): Promise<Ticket> {
    const { data, error } = await supabase
      .from('tickets')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Delete ticket
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('tickets')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }
}

// Companion Services
export const companionService = {
  // Get companions by ticket
  async getByTicket(ticketId: string): Promise<Companion[]> {
    const { data, error } = await supabase
      .from('companions')
      .select('*')
      .eq('ticket_id', ticketId)
      .order('created_at', { ascending: true })
    
    if (error) throw error
    return data || []
  },

  // Create new companion
  async create(companion: Omit<Companion, 'id' | 'created_at' | 'updated_at'>): Promise<Companion> {
    const { data, error } = await supabase
      .from('companions')
      .insert(companion)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Update companion
  async update(id: string, updates: Partial<Companion>): Promise<Companion> {
    const { data, error } = await supabase
      .from('companions')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Delete companion
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('companions')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  },

  // Delete all companions for a ticket
  async deleteByTicket(ticketId: string): Promise<void> {
    const { error } = await supabase
      .from('companions')
      .delete()
      .eq('ticket_id', ticketId)
    
    if (error) throw error
  }
}

// Room Services
export const roomService = {
  // Get all rooms
  async getAll(): Promise<Room[]> {
    const { data, error } = await supabase
      .from('rooms')
      .select('*')
      .order('room_number', { ascending: true })
    
    if (error) throw error
    return data || []
  },

  // Create new room
  async create(room: Omit<Room, 'id' | 'created_at' | 'updated_at'>): Promise<Room> {
    const { data, error } = await supabase
      .from('rooms')
      .insert(room)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Update room
  async update(id: string, updates: Partial<Room>): Promise<Room> {
    const { data, error } = await supabase
      .from('rooms')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  // Delete room
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('rooms')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// Room Assignment Services
export const roomAssignmentService = {
  // Get assignments by room
  async getByRoom(roomId: string): Promise<RoomAssignment[]> {
    const { data, error } = await supabase
      .from('room_assignments')
      .select('*')
      .eq('room_id', roomId)

    if (error) throw error
    return data || []
  },

  // Get assignment by ticket
  async getByTicket(ticketId: string): Promise<RoomAssignment | null> {
    const { data, error } = await supabase
      .from('room_assignments')
      .select('*')
      .eq('ticket_id', ticketId)
      .single()

    if (error && error.code !== 'PGRST116') throw error
    return data
  },

  // Assign passenger to room
  async assign(roomId: string, ticketId: string): Promise<RoomAssignment> {
    const { data, error } = await supabase
      .from('room_assignments')
      .insert({ room_id: roomId, ticket_id: ticketId })
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Remove assignment
  async remove(ticketId: string): Promise<void> {
    const { error } = await supabase
      .from('room_assignments')
      .delete()
      .eq('ticket_id', ticketId)

    if (error) throw error
  }
}

// Driver Services
export const driverService = {
  // Get all drivers
  async getAll(): Promise<Driver[]> {
    const { data, error } = await supabase
      .from('drivers')
      .select('*')
      .order('name', { ascending: true })

    if (error) throw error
    return data || []
  },

  // Create new driver
  async create(driver: Omit<Driver, 'id' | 'created_at' | 'updated_at'>): Promise<Driver> {
    const { data, error } = await supabase
      .from('drivers')
      .insert(driver)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Update driver
  async update(id: string, updates: Partial<Driver>): Promise<Driver> {
    const { data, error } = await supabase
      .from('drivers')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Delete driver
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('drivers')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// Trip Driver Services
export const tripDriverService = {
  // Get drivers by trip
  async getByTrip(tripId: string): Promise<TripDriver[]> {
    const { data, error } = await supabase
      .from('trip_drivers')
      .select(`
        *,
        driver:drivers(*)
      `)
      .eq('trip_id', tripId)

    if (error) throw error
    return data || []
  },

  // Assign driver to trip
  async assign(tripId: string, driverId: string): Promise<TripDriver> {
    const { data, error } = await supabase
      .from('trip_drivers')
      .insert({ trip_id: tripId, driver_id: driverId })
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Remove driver from trip
  async remove(tripId: string, driverId: string): Promise<void> {
    const { error } = await supabase
      .from('trip_drivers')
      .delete()
      .eq('trip_id', tripId)
      .eq('driver_id', driverId)

    if (error) throw error
  }
}

// Expense Services
export const expenseService = {
  // Get all expenses
  async getAll(): Promise<Expense[]> {
    const { data, error } = await supabase
      .from('expenses')
      .select('*')
      .order('expense_date', { ascending: false })

    if (error) throw error
    return data || []
  },

  // Create new expense
  async create(expense: Omit<Expense, 'id' | 'created_at' | 'updated_at'>): Promise<Expense> {
    const { data, error } = await supabase
      .from('expenses')
      .insert(expense)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Update expense
  async update(id: string, updates: Partial<Expense>): Promise<Expense> {
    const { data, error } = await supabase
      .from('expenses')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Delete expense
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('expenses')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

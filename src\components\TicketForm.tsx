import React, { useState } from 'react';
import { Plus, Printer, Download, Save, Users, RefreshCw } from 'lucide-react';
import { saveTicket, saveTrip, getTrips, generateId, generateTripNumber, assignAccommodation } from '../utils/storage';
import { printTicket, exportToPDF } from '../utils/printUtils';

interface Companion {
  name: string;
  idNumber: string;
  nationality: string;
}

interface TicketData {
  tripNumber: string;
  passengerName: string;
  passengerNameEn: string;
  nationality: string;
  phoneNumber: string;
  idNumber: string;
  departureDate: string;
  tripType: 'one-way' | 'return' | 'round-trip';
  returnDate: string;
  departureTime: string;
  accommodationType: 'individual' | 'shared' | 'family' | 'none';
  seatNumber: string;
  paymentMethod: 'cash' | 'visa' | 'network';
  paidAmount: number;
  remainingAmount: number;
  totalAmount: number;
  companions: Companion[];
}

interface Trip {
  tripNumber: string;
  passengerCount: number;
  maxCapacity: number;
}

export const TicketForm: React.FC = () => {
  // Existing trips data - in real app this would come from database
  const [existingTrips, setExistingTrips] = useState<Trip[]>(() => {
    const trips = getTrips();
    return trips.map(trip => ({
      tripNumber: trip.tripNumber,
      passengerCount: trip.passengerCount,
      maxCapacity: trip.maxCapacity
    }));
  });

  const [currentTrip, setCurrentTrip] = useState<Trip | null>(null);
  const [ticketId, setTicketId] = useState<string>(generateId());
  const [isSaving, setIsSaving] = useState(false);
  const [savedTicket, setSavedTicket] = useState<TicketData | null>(null);

  const [formData, setFormData] = useState<TicketData>({
    tripNumber: '',
    passengerName: '',
    passengerNameEn: '',
    nationality: '',
    phoneNumber: '',
    idNumber: '',
    departureDate: '',
    tripType: 'round-trip',
    returnDate: '',
    departureTime: '',
    accommodationType: 'shared',
    seatNumber: '',
    paymentMethod: 'cash',
    paidAmount: 0,
    remainingAmount: 0,
    totalAmount: 0,
    companions: [],
  });

  const [newCompanion, setNewCompanion] = useState<Companion>({
    name: '',
    idNumber: '',
    nationality: '',
  });

  // Generate new trip number
  const generateNewTripNumber = (): string => {
    return generateTripNumber();
  };

  // Find available trip or create new one
  const findOrCreateTrip = () => {
    // Find available trip (less than 49 passengers)
    const availableTrip = existingTrips.find(trip => trip.passengerCount < trip.maxCapacity);
    
    if (availableTrip) {
      setCurrentTrip(availableTrip);
      setFormData(prev => ({ ...prev, tripNumber: availableTrip.tripNumber }));
    } else {
      // Create new trip
      const newTripNumber = generateNewTripNumber();
      const newTrip: Trip = {
        tripNumber: newTripNumber,
        passengerCount: 0,
        maxCapacity: 49
      };
      setExistingTrips(prev => [...prev, newTrip]);
      setCurrentTrip(newTrip);
      
      // Save new trip to storage
      saveTrip({
        tripNumber: newTripNumber,
        passengerCount: 0,
        maxCapacity: 49,
        passengers: [],
        occupiedSeats: [],
        createdAt: new Date().toISOString()
      });
      
      setFormData(prev => ({ ...prev, tripNumber: newTripNumber }));
    }
  };

  // Update passenger count in trip
  const updateTripPassengerCount = (tripNumber: string, increment: boolean = true) => {
    setExistingTrips(prev => prev.map(trip => 
      trip.tripNumber === tripNumber 
        ? { ...trip, passengerCount: trip.passengerCount + (increment ? 1 : -1) }
        : trip
    ));
  };

  // Get available seats based on current trip
  const getAvailableSeats = (): number[] => {
    if (!currentTrip) return [];
    
    // In real app, occupied seats would be fetched from database
    const occupiedSeats = [5, 12, 23, 31, 45]; // Mock occupied seats
    const availableSeats = [];
    
    for (let i = 1; i <= 49; i++) {
      if (!occupiedSeats.includes(i)) {
        availableSeats.push(i);
      }
    }
    
    return availableSeats;
  };

  const availableSeats = Array.from({ length: 49 }, (_, i) => i + 1);
  const occupiedSeats = [5, 12, 23, 31, 45]; // Mock occupied seats

  const handleInputChange = (field: keyof TicketData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addCompanion = () => {
    if (newCompanion.name && newCompanion.idNumber) {
      setFormData(prev => ({
        ...prev,
        companions: [...prev.companions, newCompanion]
      }));
      setNewCompanion({ name: '', idNumber: '', nationality: '' });
    }
  };

  const removeCompanion = (index: number) => {
    setFormData(prev => ({
      ...prev,
      companions: prev.companions.filter((_, i) => i !== index)
    }));
  };

  const handleSave = () => {
    if (!currentTrip) {
      alert('يرجى تحديد رحلة أولاً');
      return;
    }
    
    if (!formData.passengerName || !formData.idNumber || !formData.phoneNumber) {
      alert('يرجى ملء جميع البيانات المطلوبة');
      return;
    }
    
    if (!formData.seatNumber) {
      alert('يرجى اختيار مقعد');
      return;
    }
    
    setIsSaving(true);
    
    if (currentTrip && currentTrip.passengerCount < currentTrip.maxCapacity) {
      try {
        // Create ticket object
        const ticketData = {
          id: ticketId,
          ...formData,
          createdAt: new Date().toISOString()
        };
        
        // Save ticket
        saveTicket(ticketData);
        
        // Update trip passenger count
        updateTripPassengerCount(formData.tripNumber, true);
        
        // Update trip in storage
        const trips = getTrips();
        const tripIndex = trips.findIndex(t => t.tripNumber === formData.tripNumber);
        if (tripIndex >= 0) {
          trips[tripIndex].passengers.push(ticketId);
          trips[tripIndex].passengerCount += 1;
          trips[tripIndex].occupiedSeats.push(parseInt(formData.seatNumber));
          saveTrip(trips[tripIndex]);
        }
        
        // Assign accommodation if needed
        if (formData.accommodationType !== 'none') {
          const roomId = assignAccommodation(ticketId, formData.accommodationType);
          if (roomId) {
            console.log(`تم تخصيص الغرفة ${roomId} للمسافر`);
          } else {
            console.log('لا توجد غرف متاحة من النوع المطلوب');
          }
        }
        
        alert('تم حفظ التذكرة بنجاح!');
        
        // Save current ticket for printing/export
        setSavedTicket({ ...formData });
        
        // Generate new ticket ID and reset form for new ticket
        setTicketId(generateId());
        setFormData({
          tripNumber: currentTrip.tripNumber,
          passengerName: '',
          passengerNameEn: '',
          nationality: '',
          phoneNumber: '',
          idNumber: '',
          departureDate: '',
          tripType: 'round-trip',
          returnDate: '',
          departureTime: '',
          accommodationType: 'shared',
          seatNumber: '',
          paymentMethod: 'cash',
          paidAmount: 0,
          remainingAmount: 0,
          totalAmount: 0,
          companions: [],
        });
        
      } catch (error) {
        console.error('خطأ في حفظ التذكرة:', error);
        alert('حدث خطأ أثناء حفظ التذكرة');
      }
    } else {
      alert('عذراً، الرحلة ممتلئة. سيتم إنشاء رحلة جديدة.');
      findOrCreateTrip();
    }
    
    setIsSaving(false);
  };

  const handlePrint = () => {
    const ticketToPrint = savedTicket || formData;
    if (!ticketToPrint.passengerName) {
      alert('يرجى حفظ التذكرة أولاً أو ملء البيانات');
      return;
    }
    printTicket(ticketToPrint);
  };

  const handleExportPDF = () => {
    const ticketToExport = savedTicket || formData;
    if (!ticketToExport.passengerName) {
      alert('يرجى حفظ التذكرة أولاً أو ملء البيانات');
      return;
    }
    exportToPDF(ticketToExport);
  };

  return (
    <div className="max-width-6xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl font-bold text-gray-800 flex items-center space-x-2 space-x-reverse">
            <span>تذكرة العمرة</span>
          </h2>
          <div className="flex space-x-2 space-x-reverse">
            <button
              onClick={findOrCreateTrip}
              className="flex items-center space-x-2 space-x-reverse bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
            >
              <RefreshCw className="h-4 w-4" />
              <span>تحديد رحلة</span>
            </button>
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="flex items-center space-x-2 space-x-reverse bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors"
            >
              <Save className="h-4 w-4" />
              <span>{isSaving ? 'جاري الحفظ...' : 'حفظ'}</span>
            </button>
            <button
              onClick={handlePrint}
              className="flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Printer className="h-4 w-4" />
              <span>طباعة</span>
            </button>
            <button
              onClick={handleExportPDF}
              className="flex items-center space-x-2 space-x-reverse bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
            >
              <Download className="h-4 w-4" />
              <span>تصدير PDF</span>
            </button>
          </div>
        </div>

        {/* Current trip information */}
        {currentTrip && (
          <div className="bg-gradient-to-r from-emerald-50 to-blue-50 rounded-lg p-6 mb-8 border border-emerald-200">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-emerald-600">{currentTrip.tripNumber}</div>
                <div className="text-sm text-gray-600">رقم الرحلة</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{currentTrip.passengerCount}</div>
                <div className="text-sm text-gray-600">الركاب الحاليون</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{currentTrip.maxCapacity - currentTrip.passengerCount}</div>
                <div className="text-sm text-gray-600">المقاعد المتاحة</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-600">{currentTrip.maxCapacity}</div>
                <div className="text-sm text-gray-600">السعة الإجمالية</div>
              </div>
            </div>
            
            {/* Progress bar */}
            <div className="mt-4">
              <div className="flex justify-between text-sm text-gray-600 mb-1">
                <span>امتلاء الرحلة</span>
                <span>{Math.round((currentTrip.passengerCount / currentTrip.maxCapacity) * 100)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className={`h-3 rounded-full transition-all duration-300 ${
                    currentTrip.passengerCount === currentTrip.maxCapacity 
                      ? 'bg-red-500' 
                      : currentTrip.passengerCount > currentTrip.maxCapacity * 0.8 
                      ? 'bg-yellow-500' 
                      : 'bg-emerald-500'
                  }`}
                  style={{ width: `${(currentTrip.passengerCount / currentTrip.maxCapacity) * 100}%` }}
                ></div>
              </div>
            </div>
            
            {currentTrip.passengerCount >= currentTrip.maxCapacity && (
              <div className="mt-4 p-3 bg-red-100 border border-red-300 rounded-lg">
                <p className="text-red-800 text-sm font-medium">
                  ⚠️ تنبيه: الرحلة ممتلئة! سيتم إنشاء رحلة جديدة عند إضافة راكب جديد.
                </p>
              </div>
            )}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Trip Number - read only */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">رقم الرحلة</label>
            <input
              type="text"
              value={formData.tripNumber}
              readOnly
              className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600 cursor-not-allowed"
              placeholder="سيتم توليد رقم الرحلة تلقائياً"
            />
            {!currentTrip && (
              <p className="text-sm text-gray-500 mt-1">
                اضغط على "تحديد رحلة" لتوليد رقم رحلة جديد أو اختيار رحلة متاحة
              </p>
            )}
          </div>

          {/* Passenger Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">اسم المسافر</label>
            <input
              type="text"
              value={formData.passengerName}
              onChange={(e) => handleInputChange('passengerName', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
              placeholder="أدخل اسم المسافر"
            />
          </div>

          {/* Passenger Name English */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الاسم بالإنجليزية</label>
            <input
              type="text"
              value={formData.passengerNameEn}
              onChange={(e) => handleInputChange('passengerNameEn', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
              placeholder="Enter passenger name in English"
              dir="ltr"
            />
          </div>

          {/* Nationality */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الجنسية</label>
            <select
              value={formData.nationality}
              onChange={(e) => handleInputChange('nationality', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
            >
              <option value="">اختر الجنسية</option>
              <option value="saudi">سعودي</option>
              <option value="egyptian">مصري</option>
              <option value="syrian">سوري</option>
              <option value="jordanian">أردني</option>
              <option value="lebanese">لبناني</option>
              <option value="other">أخرى</option>
            </select>
          </div>

          {/* Phone Number */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">رقم الجوال</label>
            <input
              type="tel"
              value={formData.phoneNumber}
              onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
              placeholder="05xxxxxxxx"
            />
          </div>

          {/* ID Number */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهوية/الإقامة</label>
            <input
              type="text"
              value={formData.idNumber}
              onChange={(e) => handleInputChange('idNumber', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
              placeholder="أدخل رقم الهوية"
            />
          </div>

          {/* Departure Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ الذهاب</label>
            <input
              type="date"
              value={formData.departureDate}
              onChange={(e) => handleInputChange('departureDate', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
            />
          </div>

          {/* Trip Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">نوع الرحلة</label>
            <select
              value={formData.tripType}
              onChange={(e) => handleInputChange('tripType', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
            >
              <option value="one-way">ذهاب فقط</option>
              <option value="return">عودة فقط</option>
              <option value="round-trip">ذهاب وعودة</option>
            </select>
          </div>

          {/* Return Date */}
          {(formData.tripType === 'return' || formData.tripType === 'round-trip') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ العودة</label>
              <input
                type="date"
                value={formData.returnDate}
                onChange={(e) => handleInputChange('returnDate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
              />
            </div>
          )}

          {/* Departure Time */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">موعد الرحلة</label>
            <input
              type="time"
              value={formData.departureTime}
              onChange={(e) => handleInputChange('departureTime', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
            />
          </div>

          {/* Accommodation Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">نوع السكن</label>
            <select
              value={formData.accommodationType}
              onChange={(e) => handleInputChange('accommodationType', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
            >
              <option value="individual">فردي</option>
              <option value="shared">مشترك</option>
              <option value="family">عوائل</option>
              <option value="none">بدون سكن</option>
            </select>
          </div>

          {/* Payment Method */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">طريقة الدفع</label>
            <select
              value={formData.paymentMethod}
              onChange={(e) => handleInputChange('paymentMethod', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
            >
              <option value="cash">كاش</option>
              <option value="visa">فيزا</option>
              <option value="network">شبكة</option>
            </select>
          </div>
        </div>

        {/* Seat Selection */}
        {currentTrip && (
        <div className="mt-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">اختيار المقعد</h3>
          <div className="mb-4 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <div className="w-4 h-4 bg-emerald-600 rounded"></div>
                  <span>مختار</span>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <div className="w-4 h-4 bg-red-100 border border-red-300 rounded"></div>
                  <span>محجوز</span>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <div className="w-4 h-4 bg-gray-100 border border-gray-300 rounded"></div>
                  <span>متاح</span>
                </div>
              </div>
              <span className="text-gray-600">
                المقاعد المتاحة: {49 - occupiedSeats.length - (currentTrip?.passengerCount || 0)}
              </span>
            </div>
          </div>
          <div className="grid grid-cols-10 gap-2">
            {availableSeats.map((seat) => (
              <button
                key={seat}
                onClick={() => handleInputChange('seatNumber', seat.toString())}
                disabled={occupiedSeats.includes(seat)}
                className={`
                  w-10 h-10 rounded-lg text-sm font-medium transition-all duration-200
                  ${
                    occupiedSeats.includes(seat)
                      ? 'bg-red-100 text-red-800 cursor-not-allowed'
                      : formData.seatNumber === seat.toString()
                      ? 'bg-emerald-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }
                `}
              >
                {seat}
              </button>
            ))}
          </div>
        </div>
        )}

        {/* Financial Information */}
        <div className="mt-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">المعلومات المالية</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">المبلغ الإجمالي</label>
              <input
                type="number"
                value={formData.totalAmount}
                onChange={(e) => handleInputChange('totalAmount', Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                placeholder="0"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">المبلغ المدفوع</label>
              <input
                type="number"
                value={formData.paidAmount}
                onChange={(e) => {
                  const paid = Number(e.target.value);
                  handleInputChange('paidAmount', paid);
                  handleInputChange('remainingAmount', formData.totalAmount - paid);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                placeholder="0"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">المبلغ المتبقي</label>
              <input
                type="number"
                value={formData.remainingAmount}
                readOnly
                className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600"
                placeholder="0"
              />
            </div>
          </div>
        </div>

        {/* Companions */}
        <div className="mt-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center space-x-2 space-x-reverse">
            <Users className="h-5 w-5" />
            <span>المرافقون</span>
          </h3>
          
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <input
                type="text"
                value={newCompanion.name}
                onChange={(e) => setNewCompanion(prev => ({ ...prev, name: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                placeholder="اسم المرافق"
              />
              <input
                type="text"
                value={newCompanion.idNumber}
                onChange={(e) => setNewCompanion(prev => ({ ...prev, idNumber: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                placeholder="رقم الهوية"
              />
              <div className="flex space-x-2 space-x-reverse">
                <input
                  type="text"
                  value={newCompanion.nationality}
                  onChange={(e) => setNewCompanion(prev => ({ ...prev, nationality: e.target.value }))}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                  placeholder="الجنسية"
                />
                <button
                  onClick={addCompanion}
                  className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors"
                >
                  <Plus className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>

          {formData.companions.length > 0 && (
            <div className="space-y-2">
              {formData.companions.map((companion, index) => (
                <div key={index} className="flex items-center justify-between bg-white p-3 rounded-lg border border-gray-200">
                  <div className="flex items-center space-x-4 space-x-reverse">
                    <span className="font-medium">{companion.name}</span>
                    <span className="text-gray-600">{companion.idNumber}</span>
                    <span className="text-gray-600">{companion.nationality}</span>
                  </div>
                  <button
                    onClick={() => removeCompanion(index)}
                    className="text-red-600 hover:text-red-800 transition-colors"
                  >
                    حذف
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Current trips list */}
        <div className="mt-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">الرحلات الحالية</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {existingTrips.map((trip) => (
              <div 
                key={trip.tripNumber} 
                className={`p-4 rounded-lg border-2 transition-all duration-200 cursor-pointer ${
                  currentTrip?.tripNumber === trip.tripNumber
                    ? 'border-emerald-500 bg-emerald-50'
                    : 'border-gray-200 bg-white hover:border-gray-300'
                }`}
                onClick={() => {
                  if (trip.passengerCount < trip.maxCapacity) {
                    setCurrentTrip(trip);
                    setFormData(prev => ({ ...prev, tripNumber: trip.tripNumber }));
                  }
                }}
              >
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-gray-800">{trip.tripNumber}</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    trip.passengerCount >= trip.maxCapacity
                      ? 'bg-red-100 text-red-800'
                      : trip.passengerCount > trip.maxCapacity * 0.8
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-green-100 text-green-800'
                  }`}>
                    {trip.passengerCount >= trip.maxCapacity ? 'ممتلئة' : 'متاحة'}
                  </span>
                </div>
                <div className="text-sm text-gray-600 mb-2">
                  {trip.passengerCount} / {trip.maxCapacity} راكب
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      trip.passengerCount >= trip.maxCapacity
                        ? 'bg-red-500'
                        : trip.passengerCount > trip.maxCapacity * 0.8
                        ? 'bg-yellow-500'
                        : 'bg-emerald-500'
                    }`}
                    style={{ width: `${(trip.passengerCount / trip.maxCapacity) * 100}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
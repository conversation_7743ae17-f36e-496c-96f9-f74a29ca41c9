import React, { useState } from 'react';
import { FileText, Search, Printer, Download, Users, Car } from 'lucide-react';
import { getTripByNumber, getTickets } from '../utils/storage';

interface Driver {
  name: string;
  idNumber: string;
  phoneNumber: string;
}

interface Passenger {
  name: string;
  seatNumber: string;
  companions: string[];
}

export const TripReports: React.FC = () => {
  const [reportType, setReportType] = useState<'departure' | 'return'>('departure');
  const [tripNumber, setTripNumber] = useState('');
  const [passengers, setPassengers] = useState<Passenger[]>([]);
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [newDriver, setNewDriver] = useState<Driver>({ name: '', idNumber: '', phoneNumber: '' });
  const [showDriverForm, setShowDriverForm] = useState(false);
  const [loading, setLoading] = useState(false);

  // No mock data - will use real data from storage

  const handleSearch = () => {
    if (!tripNumber) return;
    
    setLoading(true);
    setTimeout(() => {
      const trip = getTripByNumber(tripNumber);
      if (trip) {
        const tickets = getTickets();
        const tripPassengers = trip.passengers.map(ticketId => {
          const ticket = tickets.find(t => t.id === ticketId);
          if (ticket) {
            return {
              name: ticket.passengerName,
              seatNumber: ticket.seatNumber,
              companions: ticket.companions.map(c => c.name)
            };
          }
          return null;
        }).filter(Boolean) as Passenger[];
        
        setPassengers(tripPassengers);
      } else {
        setPassengers([]);
        alert('لم يتم العثور على الرحلة');
      }
      setLoading(false);
    }, 1000);
  };

  const handleConfirmData = () => {
    setShowDriverForm(true);
  };

  const addDriver = () => {
    if (newDriver.name && newDriver.idNumber && newDriver.phoneNumber) {
      setDrivers([...drivers, newDriver]);
      setNewDriver({ name: '', idNumber: '', phoneNumber: '' });
    }
  };

  const removeDriver = (index: number) => {
    setDrivers(drivers.filter((_, i) => i !== index));
  };

  const handlePrint = () => {
    window.print();
  };

  const handleExportPDF = () => {
    console.log('Exporting PDF...');
  };

  const totalPassengers = passengers.length;
  const totalCompanions = passengers.reduce((sum, p) => sum + p.companions.length, 0);
  const totalPeople = totalPassengers + totalCompanions;

  return (
    <div className="max-w-7xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-800 mb-8 flex items-center space-x-2 space-x-reverse">
          <FileText className="h-6 w-6" />
          <span>كشوفات الرحلات</span>
        </h2>

        {/* Report Type Selection */}
        <div className="flex space-x-4 space-x-reverse mb-6">
          <button
            onClick={() => setReportType('departure')}
            className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
              reportType === 'departure'
                ? 'bg-emerald-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            كشف الذهاب
          </button>
          <button
            onClick={() => setReportType('return')}
            className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
              reportType === 'return'
                ? 'bg-emerald-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            كشف العودة
          </button>
        </div>

        {/* Search Section */}
        <div className="bg-gray-50 rounded-lg p-6 mb-8">
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">رقم الرحلة</label>
              <input
                type="text"
                value={tripNumber}
                onChange={(e) => setTripNumber(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                placeholder="أدخل رقم الرحلة"
              />
            </div>
            <button
              onClick={handleSearch}
              disabled={loading || !tripNumber}
              className="flex items-center space-x-2 space-x-reverse bg-emerald-600 text-white px-6 py-2 rounded-lg hover:bg-emerald-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed mt-6"
            >
              <Search className="h-4 w-4" />
              <span>{loading ? 'جاري البحث...' : 'بحث'}</span>
            </button>
          </div>
        </div>

        {passengers.length > 0 && (
          <>
            {/* Passengers Review */}
            <div className="bg-gray-50 rounded-lg p-6 mb-8">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-800 flex items-center space-x-2 space-x-reverse">
                  <Users className="h-5 w-5" />
                  <span>مراجعة بيانات المسافرين</span>
                </h3>
                <div className="flex space-x-4 space-x-reverse text-sm">
                  <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
                    {totalPassengers} مسافر
                  </span>
                  <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full">
                    {totalCompanions} مرافق
                  </span>
                  <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full">
                    {totalPeople} إجمالي
                  </span>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-white">
                      <th className="border border-gray-300 px-4 py-2 text-right">المقعد</th>
                      <th className="border border-gray-300 px-4 py-2 text-right">اسم المسافر</th>
                      <th className="border border-gray-300 px-4 py-2 text-right">المرافقون</th>
                      <th className="border border-gray-300 px-4 py-2 text-right">العدد</th>
                    </tr>
                  </thead>
                  <tbody>
                    {passengers.map((passenger, index) => (
                      <tr key={index} className="hover:bg-white">
                        <td className="border border-gray-300 px-4 py-2 text-center font-medium">
                          {passenger.seatNumber}
                        </td>
                        <td className="border border-gray-300 px-4 py-2">{passenger.name}</td>
                        <td className="border border-gray-300 px-4 py-2">
                          {passenger.companions.length > 0 ? (
                            <div className="space-y-1">
                              {passenger.companions.map((companion, i) => (
                                <div key={i} className="text-sm text-gray-600">{companion}</div>
                              ))}
                            </div>
                          ) : (
                            <span className="text-gray-400">لا يوجد</span>
                          )}
                        </td>
                        <td className="border border-gray-300 px-4 py-2 text-center">
                          {1 + passenger.companions.length}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {!showDriverForm && (
                <div className="mt-6 flex justify-center">
                  <button
                    onClick={handleConfirmData}
                    className="bg-emerald-600 text-white px-8 py-3 rounded-lg hover:bg-emerald-700 transition-colors font-medium"
                  >
                    تأكيد البيانات والمتابعة
                  </button>
                </div>
              )}
            </div>

            {/* Driver Information */}
            {showDriverForm && (
              <div className="bg-gray-50 rounded-lg p-6 mb-8">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center space-x-2 space-x-reverse">
                  <Car className="h-5 w-5" />
                  <span>بيانات السائقين</span>
                </h3>

                <div className="bg-white rounded-lg p-4 mb-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <input
                      type="text"
                      value={newDriver.name}
                      onChange={(e) => setNewDriver(prev => ({ ...prev, name: e.target.value }))}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      placeholder="اسم السائق"
                    />
                    <input
                      type="text"
                      value={newDriver.idNumber}
                      onChange={(e) => setNewDriver(prev => ({ ...prev, idNumber: e.target.value }))}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      placeholder="رقم الهوية"
                    />
                    <div className="flex space-x-2 space-x-reverse">
                      <input
                        type="tel"
                        value={newDriver.phoneNumber}
                        onChange={(e) => setNewDriver(prev => ({ ...prev, phoneNumber: e.target.value }))}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                        placeholder="رقم الجوال"
                      />
                      <button
                        onClick={addDriver}
                        className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors"
                      >
                        إضافة
                      </button>
                    </div>
                  </div>
                </div>

                {drivers.length > 0 && (
                  <div className="space-y-2">
                    {drivers.map((driver, index) => (
                      <div key={index} className="bg-white p-3 rounded-lg flex items-center justify-between">
                        <div className="flex items-center space-x-4 space-x-reverse">
                          <span className="font-medium">{driver.name}</span>
                          <span className="text-gray-600">{driver.idNumber}</span>
                          <span className="text-gray-600">{driver.phoneNumber}</span>
                        </div>
                        <button
                          onClick={() => removeDriver(index)}
                          className="text-red-600 hover:text-red-800 transition-colors"
                        >
                          حذف
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Final Report */}
            {showDriverForm && drivers.length > 0 && (
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-800">
                    الكشف النهائي - {reportType === 'departure' ? 'الذهاب' : 'العودة'}
                  </h3>
                  <div className="flex space-x-2 space-x-reverse">
                    <button
                      onClick={handlePrint}
                      className="flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <Printer className="h-4 w-4" />
                      <span>طباعة</span>
                    </button>
                    <button
                      onClick={handleExportPDF}
                      className="flex items-center space-x-2 space-x-reverse bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
                    >
                      <Download className="h-4 w-4" />
                      <span>تصدير PDF</span>
                    </button>
                  </div>
                </div>

                {/* Trip Info */}
                <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <span className="text-sm text-gray-600">رقم الرحلة:</span>
                      <div className="font-medium">{tripNumber}</div>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">نوع الكشف:</span>
                      <div className="font-medium">{reportType === 'departure' ? 'الذهاب' : 'العودة'}</div>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">التاريخ:</span>
                      <div className="font-medium">{new Date().toLocaleDateString('ar-SA')}</div>
                    </div>
                  </div>
                </div>

                {/* Drivers Info */}
                <div className="mb-6">
                  <h4 className="font-medium text-gray-800 mb-3">بيانات السائقين:</h4>
                  <div className="space-y-2">
                    {drivers.map((driver, index) => (
                      <div key={index} className="p-3 bg-gray-50 rounded-lg">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <span className="text-sm text-gray-600">الاسم:</span>
                            <div className="font-medium">{driver.name}</div>
                          </div>
                          <div>
                            <span className="text-sm text-gray-600">رقم الهوية:</span>
                            <div className="font-medium">{driver.idNumber}</div>
                          </div>
                          <div>
                            <span className="text-sm text-gray-600">رقم الجوال:</span>
                            <div className="font-medium">{driver.phoneNumber}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Passengers Summary */}
                <div>
                  <h4 className="font-medium text-gray-800 mb-3">ملخص المسافرين:</h4>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
                    <div className="bg-blue-50 p-3 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{totalPassengers}</div>
                      <div className="text-sm text-blue-600">المسافرون</div>
                    </div>
                    <div className="bg-green-50 p-3 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{totalCompanions}</div>
                      <div className="text-sm text-green-600">المرافقون</div>
                    </div>
                    <div className="bg-purple-50 p-3 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">{totalPeople}</div>
                      <div className="text-sm text-purple-600">الإجمالي</div>
                    </div>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <div className="text-2xl font-bold text-gray-600">{drivers.length}</div>
                      <div className="text-sm text-gray-600">السائقين</div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};
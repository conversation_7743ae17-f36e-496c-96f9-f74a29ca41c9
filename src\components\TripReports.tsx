import React, { useState } from 'react';
import { FileText, Search, Printer, Download, Users, Car, Plus, Edit, Trash2 } from 'lucide-react';
import { getTripByNumber, getTickets, StoredTicket } from '../utils/storage';
import { printTripManifest } from '../utils/printUtils';

interface Driver {
  id: string;
  name: string;
  idNumber: string;
  phoneNumber: string;
  licenseNumber: string;
  licenseExpiry: string;
}

interface DetailedPassenger {
  id: string;
  name: string;
  nameEn: string;
  idNumber: string;
  nationality: string;
  phoneNumber: string;
  seatNumber: string;
  accommodationType: string;
  companions: Array<{
    name: string;
    idNumber: string;
    nationality: string;
    seatNumber?: string;
  }>;
}

export const TripReports: React.FC = () => {
  const [reportType, setReportType] = useState<'departure' | 'return'>('departure');
  const [tripNumber, setTripNumber] = useState('');
  const [passengers, setPassengers] = useState<DetailedPassenger[]>([]);
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [newDriver, setNewDriver] = useState<Driver>({
    id: '',
    name: '',
    idNumber: '',
    phoneNumber: '',
    licenseNumber: '',
    licenseExpiry: ''
  });
  const [showDriverForm, setShowDriverForm] = useState(false);
  const [editingDriver, setEditingDriver] = useState<Driver | null>(null);
  const [loading, setLoading] = useState(false);
  const [tripData, setTripData] = useState<any>(null);

  const handleSearch = () => {
    if (!tripNumber) {
      alert('يرجى إدخال رقم الرحلة');
      return;
    }

    setLoading(true);

    const trip = getTripByNumber(tripNumber);
    if (trip) {
      const tickets = getTickets();
      const tripPassengers = trip.passengers.map(ticketId => {
        const ticket = tickets.find(t => t.id === ticketId);
        if (ticket) {
          return {
            id: ticket.id,
            name: ticket.passengerName,
            nameEn: ticket.passengerNameEn,
            idNumber: ticket.idNumber,
            nationality: ticket.nationality,
            phoneNumber: ticket.phoneNumber,
            seatNumber: ticket.seatNumber,
            accommodationType: ticket.accommodationType,
            companions: ticket.companions
          };
        }
        return null;
      }).filter(Boolean) as DetailedPassenger[];

      setPassengers(tripPassengers);
      setTripData({
        tripNumber: trip.tripNumber,
        passengerCount: trip.passengerCount,
        maxCapacity: trip.maxCapacity,
        departureDate: tripPassengers[0]?.name ? tickets.find(t => t.id === trip.passengers[0])?.departureDate : '',
        passengers: tripPassengers
      });
    } else {
      setPassengers([]);
      setTripData(null);
      alert('لم يتم العثور على الرحلة');
    }
    setLoading(false);
  };

  // Driver management functions
  const addDriver = () => {
    if (!newDriver.name || !newDriver.idNumber || !newDriver.phoneNumber) {
      alert('يرجى ملء جميع البيانات المطلوبة للسائق');
      return;
    }

    const driverToAdd: Driver = {
      ...newDriver,
      id: Date.now().toString()
    };

    if (editingDriver) {
      setDrivers(prev => prev.map(driver =>
        driver.id === editingDriver.id ? { ...newDriver, id: editingDriver.id } : driver
      ));
      setEditingDriver(null);
    } else {
      setDrivers(prev => [...prev, driverToAdd]);
    }

    setNewDriver({
      id: '',
      name: '',
      idNumber: '',
      phoneNumber: '',
      licenseNumber: '',
      licenseExpiry: ''
    });
    setShowDriverForm(false);
  };

  const editDriver = (driver: Driver) => {
    setNewDriver(driver);
    setEditingDriver(driver);
    setShowDriverForm(true);
  };

  const deleteDriver = (driverId: string) => {
    if (window.confirm('هل أنت متأكد من حذف بيانات السائق؟')) {
      setDrivers(prev => prev.filter(driver => driver.id !== driverId));
    }
  };

  // Print and export functions
  const handlePrint = () => {
    if (!tripData || passengers.length === 0) {
      alert('لا توجد بيانات للطباعة. يرجى البحث عن رحلة أولاً.');
      return;
    }

    const manifestData = {
      ...tripData,
      drivers: drivers,
      reportType: reportType
    };

    printTripManifest(manifestData);
  };

  const handleExportPDF = () => {
    if (!tripData || passengers.length === 0) {
      alert('لا توجد بيانات للتصدير. يرجى البحث عن رحلة أولاً.');
      return;
    }

    // Use the same print function for PDF export
    handlePrint();
  };

  // Calculate statistics
  const totalPassengers = passengers.length;
  const totalCompanions = passengers.reduce((sum, p) => sum + p.companions.length, 0);
  const totalPeople = totalPassengers + totalCompanions;

  return (
    <div className="max-w-7xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-800 mb-8 flex items-center space-x-2 space-x-reverse">
          <FileText className="h-6 w-6" />
          <span>كشوفات الرحلات</span>
        </h2>

        {/* Report Type Selection */}
        <div className="flex space-x-4 space-x-reverse mb-6">
          <button
            onClick={() => setReportType('departure')}
            className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
              reportType === 'departure'
                ? 'bg-emerald-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            كشف الذهاب
          </button>
          <button
            onClick={() => setReportType('return')}
            className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
              reportType === 'return'
                ? 'bg-emerald-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            كشف العودة
          </button>
        </div>

        {/* Search Section */}
        <div className="bg-gray-50 rounded-lg p-6 mb-8">
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">رقم الرحلة</label>
              <input
                type="text"
                value={tripNumber}
                onChange={(e) => setTripNumber(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                placeholder="أدخل رقم الرحلة"
              />
            </div>
            <button
              onClick={handleSearch}
              disabled={loading || !tripNumber}
              className="flex items-center space-x-2 space-x-reverse bg-emerald-600 text-white px-6 py-2 rounded-lg hover:bg-emerald-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed mt-6"
            >
              <Search className="h-4 w-4" />
              <span>{loading ? 'جاري البحث...' : 'بحث'}</span>
            </button>
          </div>
        </div>

        {passengers.length > 0 && (
          <>
            {/* Passengers Review */}
            <div className="bg-gray-50 rounded-lg p-6 mb-8">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-800 flex items-center space-x-2 space-x-reverse">
                  <Users className="h-5 w-5" />
                  <span>مراجعة بيانات المسافرين</span>
                </h3>
                <div className="flex space-x-4 space-x-reverse text-sm">
                  <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
                    {totalPassengers} مسافر
                  </span>
                  <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full">
                    {totalCompanions} مرافق
                  </span>
                  <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full">
                    {totalPeople} إجمالي
                  </span>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300 text-sm">
                  <thead>
                    <tr className="bg-emerald-600 text-white">
                      <th className="border border-gray-300 px-3 py-2 text-center">المقعد</th>
                      <th className="border border-gray-300 px-3 py-2 text-right">اسم المسافر</th>
                      <th className="border border-gray-300 px-3 py-2 text-center">رقم الهوية</th>
                      <th className="border border-gray-300 px-3 py-2 text-center">الجنسية</th>
                      <th className="border border-gray-300 px-3 py-2 text-center">الجوال</th>
                      <th className="border border-gray-300 px-3 py-2 text-center">السكن</th>
                      <th className="border border-gray-300 px-3 py-2 text-right">المرافقون</th>
                      <th className="border border-gray-300 px-3 py-2 text-center">العدد</th>
                    </tr>
                  </thead>
                  <tbody>
                    {passengers.map((passenger, index) => (
                      <React.Fragment key={passenger.id}>
                        {/* Main passenger row */}
                        <tr className="hover:bg-gray-50 bg-green-50">
                          <td className="border border-gray-300 px-3 py-2 text-center font-bold text-emerald-600">
                            {passenger.seatNumber}
                          </td>
                          <td className="border border-gray-300 px-3 py-2 font-medium">
                            <div>{passenger.name}</div>
                            {passenger.nameEn && (
                              <div className="text-xs text-gray-500">{passenger.nameEn}</div>
                            )}
                          </td>
                          <td className="border border-gray-300 px-3 py-2 text-center">
                            {passenger.idNumber}
                          </td>
                          <td className="border border-gray-300 px-3 py-2 text-center">
                            {passenger.nationality}
                          </td>
                          <td className="border border-gray-300 px-3 py-2 text-center">
                            {passenger.phoneNumber}
                          </td>
                          <td className="border border-gray-300 px-3 py-2 text-center">
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              passenger.accommodationType === 'individual' ? 'bg-blue-100 text-blue-800' :
                              passenger.accommodationType === 'shared' ? 'bg-green-100 text-green-800' :
                              passenger.accommodationType === 'family' ? 'bg-purple-100 text-purple-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {passenger.accommodationType === 'individual' ? 'فردي' :
                               passenger.accommodationType === 'shared' ? 'مشترك' :
                               passenger.accommodationType === 'family' ? 'عوائل' : 'بدون سكن'}
                            </span>
                          </td>
                          <td className="border border-gray-300 px-3 py-2">
                            {passenger.companions.length > 0 ? (
                              <div className="space-y-1">
                                {passenger.companions.map((companion, i) => (
                                  <div key={i} className="text-xs bg-yellow-50 p-1 rounded border">
                                    <div className="font-medium">{companion.name}</div>
                                    <div className="text-gray-600">
                                      {companion.idNumber} - {companion.nationality}
                                      {companion.seatNumber && ` - مقعد ${companion.seatNumber}`}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <span className="text-gray-400 text-xs">لا يوجد مرافقين</span>
                            )}
                          </td>
                          <td className="border border-gray-300 px-3 py-2 text-center font-bold">
                            {1 + passenger.companions.length}
                          </td>
                        </tr>
                      </React.Fragment>
                    ))}
                    {/* Summary row */}
                    <tr className="bg-emerald-100 font-bold">
                      <td colSpan={7} className="border border-gray-300 px-3 py-2 text-right">
                        إجمالي المسافرين والمرافقين
                      </td>
                      <td className="border border-gray-300 px-3 py-2 text-center text-emerald-600">
                        {totalPeople}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

            </div>

            {/* Driver Information */}
            <div className="bg-gray-50 rounded-lg p-6 mb-8">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-800 flex items-center space-x-2 space-x-reverse">
                  <Car className="h-5 w-5" />
                  <span>بيانات السائقين</span>
                </h3>
                <button
                  onClick={() => setShowDriverForm(true)}
                  className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors flex items-center space-x-2 space-x-reverse"
                >
                  <Plus className="h-4 w-4" />
                  <span>إضافة سائق</span>
                </button>
              </div>

              {/* Drivers List */}
              {drivers.length > 0 && (
                <div className="mb-4">
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse border border-gray-300 text-sm">
                      <thead>
                        <tr className="bg-blue-600 text-white">
                          <th className="border border-gray-300 px-3 py-2 text-right">اسم السائق</th>
                          <th className="border border-gray-300 px-3 py-2 text-center">رقم الهوية</th>
                          <th className="border border-gray-300 px-3 py-2 text-center">رقم الجوال</th>
                          <th className="border border-gray-300 px-3 py-2 text-center">رقم الرخصة</th>
                          <th className="border border-gray-300 px-3 py-2 text-center">انتهاء الرخصة</th>
                          <th className="border border-gray-300 px-3 py-2 text-center">الإجراءات</th>
                        </tr>
                      </thead>
                      <tbody>
                        {drivers.map((driver) => (
                          <tr key={driver.id} className="hover:bg-gray-50">
                            <td className="border border-gray-300 px-3 py-2 font-medium">{driver.name}</td>
                            <td className="border border-gray-300 px-3 py-2 text-center">{driver.idNumber}</td>
                            <td className="border border-gray-300 px-3 py-2 text-center">{driver.phoneNumber}</td>
                            <td className="border border-gray-300 px-3 py-2 text-center">{driver.licenseNumber}</td>
                            <td className="border border-gray-300 px-3 py-2 text-center">{driver.licenseExpiry}</td>
                            <td className="border border-gray-300 px-3 py-2 text-center">
                              <div className="flex justify-center space-x-2 space-x-reverse">
                                <button
                                  onClick={() => editDriver(driver)}
                                  className="text-blue-600 hover:text-blue-800 p-1"
                                  title="تعديل"
                                >
                                  <Edit className="h-4 w-4" />
                                </button>
                                <button
                                  onClick={() => deleteDriver(driver.id)}
                                  className="text-red-600 hover:text-red-800 p-1"
                                  title="حذف"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Add/Edit Driver Form */}
              {showDriverForm && (
                <div className="bg-white rounded-lg p-4 mb-4">
                  <h4 className="font-medium text-gray-800 mb-3">
                    {editingDriver ? 'تعديل بيانات السائق' : 'إضافة سائق جديد'}
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <input
                      type="text"
                      value={newDriver.name}
                      onChange={(e) => setNewDriver(prev => ({ ...prev, name: e.target.value }))}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      placeholder="اسم السائق"
                    />
                    <input
                      type="text"
                      value={newDriver.idNumber}
                      onChange={(e) => setNewDriver(prev => ({ ...prev, idNumber: e.target.value }))}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      placeholder="رقم الهوية"
                    />
                    <input
                      type="tel"
                      value={newDriver.phoneNumber}
                      onChange={(e) => setNewDriver(prev => ({ ...prev, phoneNumber: e.target.value }))}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      placeholder="رقم الجوال"
                    />
                    <input
                      type="text"
                      value={newDriver.licenseNumber}
                      onChange={(e) => setNewDriver(prev => ({ ...prev, licenseNumber: e.target.value }))}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      placeholder="رقم رخصة القيادة"
                    />
                    <input
                      type="date"
                      value={newDriver.licenseExpiry}
                      onChange={(e) => setNewDriver(prev => ({ ...prev, licenseExpiry: e.target.value }))}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      placeholder="تاريخ انتهاء الرخصة"
                    />
                  </div>
                  <div className="flex space-x-3 space-x-reverse mt-4">
                    <button
                      onClick={addDriver}
                      className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors"
                    >
                      {editingDriver ? 'حفظ التعديل' : 'إضافة السائق'}
                    </button>
                    <button
                      onClick={() => {
                        setShowDriverForm(false);
                        setEditingDriver(null);
                        setNewDriver({
                          id: '',
                          name: '',
                          idNumber: '',
                          phoneNumber: '',
                          licenseNumber: '',
                          licenseExpiry: ''
                        });
                      }}
                      className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors"
                    >
                      إلغاء
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Print and Export Actions */}
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-800 flex items-center space-x-2 space-x-reverse">
                  <FileText className="h-5 w-5" />
                  <span>كشف الرحلة - {reportType === 'departure' ? 'الذهاب' : 'العودة'}</span>
                </h3>
                <div className="flex space-x-3 space-x-reverse">
                  <button
                    onClick={handlePrint}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 space-x-reverse"
                  >
                    <Printer className="h-4 w-4" />
                    <span>طباعة</span>
                  </button>
                  <button
                    onClick={handleExportPDF}
                    className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2 space-x-reverse"
                  >
                    <Download className="h-4 w-4" />
                    <span>تصدير PDF</span>
                  </button>
                </div>
              </div>

              {/* Trip Summary */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-emerald-50 p-4 rounded-lg border border-emerald-200">
                  <div className="text-emerald-600 text-sm font-medium">رقم الرحلة</div>
                  <div className="text-xl font-bold text-emerald-800">{tripNumber}</div>
                </div>
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <div className="text-blue-600 text-sm font-medium">إجمالي المسافرين</div>
                  <div className="text-xl font-bold text-blue-800">{totalPassengers}</div>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                  <div className="text-purple-600 text-sm font-medium">إجمالي المرافقين</div>
                  <div className="text-xl font-bold text-purple-800">{totalCompanions}</div>
                </div>
                <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
                  <div className="text-orange-600 text-sm font-medium">إجمالي الأشخاص</div>
                  <div className="text-xl font-bold text-orange-800">{totalPeople}</div>
                </div>
              </div>

              {/* Drivers Summary */}
              {drivers.length > 0 && (
                <div className="mb-4">
                  <h4 className="font-medium text-gray-800 mb-2">السائقون المكلفون:</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {drivers.map((driver) => (
                      <div key={driver.id} className="bg-gray-50 p-3 rounded-lg border">
                        <div className="font-medium">{driver.name}</div>
                        <div className="text-sm text-gray-600">
                          هوية: {driver.idNumber} | جوال: {driver.phoneNumber}
                        </div>
                        {driver.licenseNumber && (
                          <div className="text-sm text-gray-600">
                            رخصة: {driver.licenseNumber} | انتهاء: {driver.licenseExpiry}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="text-center text-gray-600 text-sm">
                تم إنشاء هذا الكشف في: {new Date().toLocaleString('ar-SA')}
              </div>
            </div>

          </>
        )}
      </div>
    </div>
  );
};
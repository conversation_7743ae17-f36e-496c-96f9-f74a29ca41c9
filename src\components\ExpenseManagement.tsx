import React, { useState } from 'react';
import { Receipt, Plus, Edit, Trash2, Calendar, DollarSign, Filter, Search } from 'lucide-react';

interface Expense {
  id: string;
  type: string;
  category: string;
  amount: number;
  date: string;
  notes: string;
  paymentMethod: string;
}

export const ExpenseManagement: React.FC = () => {
  const [expenses, setExpenses] = useState<Expense[]>([
    {
      id: '1',
      type: 'إيجار سكن',
      category: 'accommodation',
      amount: 15000,
      date: '2024-01-15',
      notes: 'إيجار شهري لسكن المسافرين',
      paymentMethod: 'bank'
    },
    {
      id: '2',
      type: 'راتب سائق',
      category: 'salary',
      amount: 4500,
      date: '2024-01-20',
      notes: 'راتب السائق أحمد محمد',
      paymentMethod: 'cash'
    },
    {
      id: '3',
      type: 'صيانة باص',
      category: 'maintenance',
      amount: 2800,
      date: '2024-01-22',
      notes: 'تغيير زيت وفلاتر',
      paymentMethod: 'visa'
    },
    {
      id: '4',
      type: 'مصروفات مكتبية',
      category: 'office',
      amount: 800,
      date: '2024-01-25',
      notes: 'أدوات مكتبية وطباعة',
      paymentMethod: 'cash'
    },
  ]);

  const [showForm, setShowForm] = useState(false);
  const [editingExpense, setEditingExpense] = useState<Expense | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterDate, setFilterDate] = useState('');

  const [formData, setFormData] = useState<Omit<Expense, 'id'>>({
    type: '',
    category: 'accommodation',
    amount: 0,
    date: '',
    notes: '',
    paymentMethod: 'cash'
  });

  const categories = [
    { id: 'accommodation', label: 'إيجار السكن', color: 'bg-blue-100 text-blue-800' },
    { id: 'salary', label: 'الرواتب', color: 'bg-green-100 text-green-800' },
    { id: 'maintenance', label: 'صيانة الباصات', color: 'bg-yellow-100 text-yellow-800' },
    { id: 'office', label: 'مصروفات مكتبية', color: 'bg-purple-100 text-purple-800' },
    { id: 'fuel', label: 'الوقود', color: 'bg-red-100 text-red-800' },
    { id: 'insurance', label: 'التأمين', color: 'bg-indigo-100 text-indigo-800' },
    { id: 'other', label: 'أخرى', color: 'bg-gray-100 text-gray-800' },
  ];

  const paymentMethods = [
    { id: 'cash', label: 'كاش' },
    { id: 'visa', label: 'فيزا' },
    { id: 'bank', label: 'تحويل بنكي' },
    { id: 'check', label: 'شيك' },
  ];

  const handleInputChange = (field: keyof Omit<Expense, 'id'>, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingExpense) {
      setExpenses(prev => prev.map(expense => 
        expense.id === editingExpense.id 
          ? { ...formData, id: editingExpense.id }
          : expense
      ));
      setEditingExpense(null);
    } else {
      const newExpense: Expense = {
        ...formData,
        id: Date.now().toString()
      };
      setExpenses(prev => [...prev, newExpense]);
    }
    setFormData({
      type: '',
      category: 'accommodation',
      amount: 0,
      date: '',
      notes: '',
      paymentMethod: 'cash'
    });
    setShowForm(false);
  };

  const handleEdit = (expense: Expense) => {
    setEditingExpense(expense);
    setFormData({
      type: expense.type,
      category: expense.category,
      amount: expense.amount,
      date: expense.date,
      notes: expense.notes,
      paymentMethod: expense.paymentMethod
    });
    setShowForm(true);
  };

  const handleDelete = (id: string) => {
    setExpenses(prev => prev.filter(expense => expense.id !== id));
  };

  const getCategoryLabel = (categoryId: string) => {
    return categories.find(cat => cat.id === categoryId)?.label || 'غير محدد';
  };

  const getCategoryColor = (categoryId: string) => {
    return categories.find(cat => cat.id === categoryId)?.color || 'bg-gray-100 text-gray-800';
  };

  const getPaymentMethodLabel = (methodId: string) => {
    return paymentMethods.find(method => method.id === methodId)?.label || 'غير محدد';
  };

  const filteredExpenses = expenses.filter(expense => {
    const matchesSearch = expense.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         expense.notes.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = filterCategory === 'all' || expense.category === filterCategory;
    const matchesDate = !filterDate || expense.date.includes(filterDate);
    
    return matchesSearch && matchesCategory && matchesDate;
  });

  const totalExpenses = filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0);

  const categoryStats = categories.map(category => ({
    ...category,
    total: expenses.filter(exp => exp.category === category.id).reduce((sum, exp) => sum + exp.amount, 0),
    count: expenses.filter(exp => exp.category === category.id).length
  }));

  return (
    <div className="max-w-7xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl font-bold text-gray-800 flex items-center space-x-2 space-x-reverse">
            <Receipt className="h-6 w-6" />
            <span>إدارة المصروفات</span>
          </h2>
          <button
            onClick={() => setShowForm(true)}
            className="flex items-center space-x-2 space-x-reverse bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>إضافة مصروف جديد</span>
          </button>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-gradient-to-r from-red-500 to-red-600 text-white p-4 rounded-lg">
            <div className="text-2xl font-bold">{totalExpenses.toLocaleString()}</div>
            <div className="text-red-100">إجمالي المصروفات</div>
          </div>
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-lg">
            <div className="text-2xl font-bold">{filteredExpenses.length}</div>
            <div className="text-blue-100">عدد المصروفات</div>
          </div>
          <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-lg">
            <div className="text-2xl font-bold">{categoryStats.filter(cat => cat.count > 0).length}</div>
            <div className="text-green-100">الفئات النشطة</div>
          </div>
          <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-lg">
            <div className="text-2xl font-bold">{filteredExpenses.length > 0 ? Math.round(totalExpenses / filteredExpenses.length) : 0}</div>
            <div className="text-purple-100">متوسط المصروف</div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-gray-50 rounded-lg p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">البحث</label>
              <div className="relative">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                  placeholder="البحث في نوع المصروف أو الملاحظات..."
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
              >
                <option value="all">جميع الفئات</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>{category.label}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">التاريخ</label>
              <input
                type="month"
                value={filterDate}
                onChange={(e) => setFilterDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
              />
            </div>
          </div>
        </div>

        {/* Category Summary */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
          {categoryStats.filter(cat => cat.count > 0).map(category => (
            <div key={category.id} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${category.color}`}>
                  {category.label}
                </span>
                <span className="text-sm text-gray-600">{category.count} مصروف</span>
              </div>
              <div className="text-lg font-bold text-gray-800">{category.total.toLocaleString()} ريال</div>
            </div>
          ))}
        </div>

        {/* Expense Form */}
        {showForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl p-8 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <h3 className="text-xl font-bold text-gray-800 mb-6">
                {editingExpense ? 'تعديل المصروف' : 'إضافة مصروف جديد'}
              </h3>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">نوع المصروف</label>
                    <input
                      type="text"
                      value={formData.type}
                      onChange={(e) => handleInputChange('type', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      placeholder="أدخل نوع المصروف"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                    <select
                      value={formData.category}
                      onChange={(e) => handleInputChange('category', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      required
                    >
                      {categories.map(category => (
                        <option key={category.id} value={category.id}>{category.label}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">المبلغ</label>
                    <input
                      type="number"
                      value={formData.amount}
                      onChange={(e) => handleInputChange('amount', Number(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      placeholder="0"
                      min="0"
                      step="0.01"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">التاريخ</label>
                    <input
                      type="date"
                      value={formData.date}
                      onChange={(e) => handleInputChange('date', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">طريقة الدفع</label>
                    <select
                      value={formData.paymentMethod}
                      onChange={(e) => handleInputChange('paymentMethod', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      required
                    >
                      {paymentMethods.map(method => (
                        <option key={method.id} value={method.id}>{method.label}</option>
                      ))}
                    </select>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الملاحظات</label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                    rows={3}
                    placeholder="أدخل ملاحظات إضافية..."
                  />
                </div>
                <div className="flex justify-end space-x-2 space-x-reverse pt-4">
                  <button
                    type="button"
                    onClick={() => {
                      setShowForm(false);
                      setEditingExpense(null);
                      setFormData({
                        type: '',
                        category: 'accommodation',
                        amount: 0,
                        date: '',
                        notes: '',
                        paymentMethod: 'cash'
                      });
                    }}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    إلغاء
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
                  >
                    {editingExpense ? 'تحديث' : 'إضافة'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Expenses List */}
        <div className="space-y-4">
          {filteredExpenses.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Receipt className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>لا توجد مصروفات لعرضها</p>
            </div>
          ) : (
            filteredExpenses.map(expense => (
              <div key={expense.id} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4 space-x-reverse">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <DollarSign className="h-5 w-5 text-gray-600" />
                      <span className="font-semibold text-lg">{expense.type}</span>
                    </div>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(expense.category)}`}>
                      {getCategoryLabel(expense.category)}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <button
                      onClick={() => handleEdit(expense)}
                      className="text-blue-600 hover:text-blue-800 transition-colors"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(expense.id)}
                      className="text-red-600 hover:text-red-800 transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">المبلغ:</span>
                    <div className="font-bold text-red-600">{expense.amount.toLocaleString()} ريال</div>
                  </div>
                  <div>
                    <span className="text-gray-600">التاريخ:</span>
                    <div className="font-medium">{new Date(expense.date).toLocaleDateString('ar-SA')}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">طريقة الدفع:</span>
                    <div className="font-medium">{getPaymentMethodLabel(expense.paymentMethod)}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">الملاحظات:</span>
                    <div className="text-gray-800">{expense.notes || 'لا توجد ملاحظات'}</div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};
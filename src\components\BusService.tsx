import React, { useState } from 'react';
import { Bus, Search, Users, Printer, Download } from 'lucide-react';
import { getTripByNumber, getTickets } from '../utils/storage';
import { printTicket } from '../utils/printUtils';

interface Passenger {
  name: string;
  seatNumber: string;
  phoneNumber: string;
}

export const BusService: React.FC = () => {
  const [tripNumber, setTripNumber] = useState('');
  const [passengers, setPassengers] = useState<Passenger[]>([]);
  const [loading, setLoading] = useState(false);

  // No mock data - will use real data from storage

  const handleSearch = () => {
    if (!tripNumber) return;
    
    setLoading(true);
    setTimeout(() => {
      const trip = getTripByNumber(tripNumber);
      if (trip) {
        const tickets = getTickets();
        const tripPassengers = trip.passengers.map(ticketId => {
          const ticket = tickets.find(t => t.id === ticketId);
          return ticket ? {
            name: ticket.passengerName,
            seatNumber: ticket.seatNumber,
            phoneNumber: ticket.phoneNumber
          } : null;
        }).filter(Boolean) as Passenger[];
        
        setPassengers(tripPassengers);
      } else {
        setPassengers([]);
        alert('لم يتم العثور على الرحلة');
      }
      setLoading(false);
    }, 1000);
  };

  const handlePrint = () => {
    window.print();
  };

  const handleExportPDF = () => {
    console.log('Exporting PDF...');
  };

  const seatLayout = Array.from({ length: 49 }, (_, i) => i + 1);
  const occupiedSeats = passengers.map(p => parseInt(p.seatNumber));

  return (
    <div className="max-w-7xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-800 mb-8 flex items-center space-x-2 space-x-reverse">
          <Bus className="h-6 w-6" />
          <span>خدمة الباص</span>
        </h2>

        {/* Search Section */}
        <div className="bg-gray-50 rounded-lg p-6 mb-8">
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">رقم الرحلة</label>
              <input
                type="text"
                value={tripNumber}
                onChange={(e) => setTripNumber(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                placeholder="أدخل رقم الرحلة للبحث عن المسافرين"
              />
            </div>
            <button
              onClick={handleSearch}
              disabled={loading || !tripNumber}
              className="flex items-center space-x-2 space-x-reverse bg-emerald-600 text-white px-6 py-2 rounded-lg hover:bg-emerald-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed mt-6"
            >
              <Search className="h-4 w-4" />
              <span>{loading ? 'جاري البحث...' : 'بحث'}</span>
            </button>
          </div>
        </div>

        {passengers.length > 0 && (
          <>
            {/* Results Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-2 space-x-reverse">
                <Users className="h-5 w-5 text-emerald-600" />
                <span className="text-lg font-semibold text-gray-800">
                  المسافرون في الرحلة {tripNumber}
                </span>
                <span className="bg-emerald-100 text-emerald-800 px-3 py-1 rounded-full text-sm font-medium">
                  {passengers.length} مسافر
                </span>
              </div>
              <div className="flex space-x-2 space-x-reverse">
                <button
                  onClick={handlePrint}
                  className="flex items-center space-x-2 space-x-reverse bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Printer className="h-4 w-4" />
                  <span>طباعة</span>
                </button>
                <button
                  onClick={handleExportPDF}
                  className="flex items-center space-x-2 space-x-reverse bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
                >
                  <Download className="h-4 w-4" />
                  <span>تصدير PDF</span>
                </button>
              </div>
            </div>

            {/* Bus Layout */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Seat Map */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">خريطة المقاعد</h3>
                <div className="bg-white rounded-lg p-4">
                  <div className="grid grid-cols-7 gap-2 mb-4">
                    {seatLayout.map((seat) => (
                      <div
                        key={seat}
                        className={`
                          w-10 h-10 rounded-lg flex items-center justify-center text-sm font-medium
                          ${
                            occupiedSeats.includes(seat)
                              ? 'bg-emerald-600 text-white'
                              : 'bg-gray-200 text-gray-600'
                          }
                        `}
                      >
                        {seat}
                      </div>
                    ))}
                  </div>
                  <div className="flex items-center justify-center space-x-6 space-x-reverse text-sm">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <div className="w-4 h-4 bg-emerald-600 rounded"></div>
                      <span>مشغول</span>
                    </div>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <div className="w-4 h-4 bg-gray-200 rounded"></div>
                      <span>متاح</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Passenger List */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">قائمة المسافرين</h3>
                <div className="space-y-3">
                  {passengers.map((passenger, index) => (
                    <div key={index} className="bg-white rounded-lg p-4 flex items-center justify-between">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <div className="w-10 h-10 bg-emerald-600 text-white rounded-lg flex items-center justify-center font-medium">
                          {passenger.seatNumber}
                        </div>
                        <div>
                          <div className="font-medium text-gray-800">{passenger.name}</div>
                          <div className="text-sm text-gray-600">{passenger.phoneNumber}</div>
                        </div>
                      </div>
                      <div className="text-sm text-gray-500">
                        مقعد {passenger.seatNumber}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Summary Table */}
            <div className="mt-8">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">جدول المسافرين</h3>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-4 py-2 text-right">رقم المقعد</th>
                      <th className="border border-gray-300 px-4 py-2 text-right">اسم المسافر</th>
                      <th className="border border-gray-300 px-4 py-2 text-right">رقم الجوال</th>
                    </tr>
                  </thead>
                  <tbody>
                    {passengers.map((passenger, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="border border-gray-300 px-4 py-2 text-center font-medium">
                          {passenger.seatNumber}
                        </td>
                        <td className="border border-gray-300 px-4 py-2">{passenger.name}</td>
                        <td className="border border-gray-300 px-4 py-2">{passenger.phoneNumber}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};